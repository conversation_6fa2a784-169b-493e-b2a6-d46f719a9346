/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/editBlog/[id]/page";
exports.ids = ["app/admin/editBlog/[id]/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&page=%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&page=%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'editBlog',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/editBlog/[id]/page.jsx */ \"(rsc)/./app/admin/editBlog/[id]/page.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/layout.jsx */ \"(rsc)/./app/admin/layout.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.jsx */ \"(rsc)/./app/layout.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/editBlog/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/editBlog/[id]/page\",\n        pathname: \"/admin/editBlog/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&page=%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CEmailSubscriptionPopup.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CEmailSubscriptionPopup.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./Components/CookieConsent.jsx */ \"(ssr)/./Components/CookieConsent.jsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./Components/EmailSubscriptionPopup.jsx */ \"(ssr)/./Components/EmailSubscriptionPopup.jsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-toastify/dist/react-toastify.esm.mjs */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CEmailSubscriptionPopup.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cadmin%5CeditBlog%5C%5Bid%5D%5Cpage.jsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cadmin%5CeditBlog%5C%5Bid%5D%5Cpage.jsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/editBlog/[id]/page.jsx */ \"(ssr)/./app/admin/editBlog/[id]/page.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNhcHAlNUNhZG1pbiU1Q2VkaXRCbG9nJTVDJTVCaWQlNUQlNUNwYWdlLmpzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLz8wZGExIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcYXBwXFxcXGFkbWluXFxcXGVkaXRCbG9nXFxcXFtpZF1cXFxccGFnZS5qc3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cadmin%5CeditBlog%5C%5Bid%5D%5Cpage.jsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cadmin%5Clayout.jsx&server=true!":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cadmin%5Clayout.jsx&server=true! ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/layout.jsx */ \"(ssr)/./app/admin/layout.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNhcHAlNUNhZG1pbiU1Q2xheW91dC5qc3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8/Y2YyOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXGFwcFxcXFxhZG1pblxcXFxsYXlvdXQuanN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cadmin%5Clayout.jsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNhcHAtcm91dGVyLmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RTWVMlNUNEZXNrdG9wJTVDTXIuQmxvZyU1Q2Jsb2ctYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RTWVMlNUNEZXNrdG9wJTVDTXIuQmxvZyU1Q2Jsb2ctYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTZJO0FBQzdJLDBPQUFpSjtBQUNqSix3T0FBZ0o7QUFDaEosa1BBQXFKO0FBQ3JKLHNRQUErSjtBQUMvSiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvPzRlMjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEU1lTXFxcXERlc2t0b3BcXFxcTXIuQmxvZ1xcXFxibG9nLWFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./Assets/assets.js":
/*!**************************!*\
  !*** ./Assets/assets.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assets: () => (/* binding */ assets),\n/* harmony export */   blog_data: () => (/* binding */ blog_data)\n/* harmony export */ });\n/* harmony import */ var _blog_pic_1_png__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blog_pic_1.png */ \"(ssr)/./Assets/blog_pic_1.png\");\n/* harmony import */ var _blog_pic_2_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./blog_pic_2.png */ \"(ssr)/./Assets/blog_pic_2.png\");\n/* harmony import */ var _blog_pic_3_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./blog_pic_3.png */ \"(ssr)/./Assets/blog_pic_3.png\");\n/* harmony import */ var _blog_pic_4_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog_pic_4.png */ \"(ssr)/./Assets/blog_pic_4.png\");\n/* harmony import */ var _blog_pic_5_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./blog_pic_5.png */ \"(ssr)/./Assets/blog_pic_5.png\");\n/* harmony import */ var _blog_pic_6_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./blog_pic_6.png */ \"(ssr)/./Assets/blog_pic_6.png\");\n/* harmony import */ var _blog_pic_7_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./blog_pic_7.png */ \"(ssr)/./Assets/blog_pic_7.png\");\n/* harmony import */ var _blog_pic_8_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./blog_pic_8.png */ \"(ssr)/./Assets/blog_pic_8.png\");\n/* harmony import */ var _blog_pic_9_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./blog_pic_9.png */ \"(ssr)/./Assets/blog_pic_9.png\");\n/* harmony import */ var _blog_pic_10_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./blog_pic_10.png */ \"(ssr)/./Assets/blog_pic_10.png\");\n/* harmony import */ var _blog_pic_11_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./blog_pic_11.png */ \"(ssr)/./Assets/blog_pic_11.png\");\n/* harmony import */ var _blog_pic_12_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./blog_pic_12.png */ \"(ssr)/./Assets/blog_pic_12.png\");\n/* harmony import */ var _blog_pic_13_png__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./blog_pic_13.png */ \"(ssr)/./Assets/blog_pic_13.png\");\n/* harmony import */ var _blog_pic_14_png__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./blog_pic_14.png */ \"(ssr)/./Assets/blog_pic_14.png\");\n/* harmony import */ var _blog_pic_15_png__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./blog_pic_15.png */ \"(ssr)/./Assets/blog_pic_15.png\");\n/* harmony import */ var _blog_pic_16_png__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./blog_pic_16.png */ \"(ssr)/./Assets/blog_pic_16.png\");\n/* harmony import */ var _facebook_icon_png__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./facebook_icon.png */ \"(ssr)/./Assets/facebook_icon.png\");\n/* harmony import */ var _googleplus_icon_png__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./googleplus_icon.png */ \"(ssr)/./Assets/googleplus_icon.png\");\n/* harmony import */ var _twitter_icon_png__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./twitter_icon.png */ \"(ssr)/./Assets/twitter_icon.png\");\n/* harmony import */ var _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./profile_icon.png */ \"(ssr)/./Assets/profile_icon.png\");\n/* harmony import */ var _logo_png__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./logo.png */ \"(ssr)/./Assets/logo.png\");\n/* harmony import */ var _arrow_png__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./arrow.png */ \"(ssr)/./Assets/arrow.png\");\n/* harmony import */ var _logo_light_png__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./logo_light.png */ \"(ssr)/./Assets/logo_light.png\");\n/* harmony import */ var _blog_icon_png__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./blog_icon.png */ \"(ssr)/./Assets/blog_icon.png\");\n/* harmony import */ var _add_icon_png__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./add_icon.png */ \"(ssr)/./Assets/add_icon.png\");\n/* harmony import */ var _email_icon_png__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./email_icon.png */ \"(ssr)/./Assets/email_icon.png\");\n/* harmony import */ var _upload_area_png__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./upload_area.png */ \"(ssr)/./Assets/upload_area.png\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst assets = {\n    facebook_icon: _facebook_icon_png__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    googleplus_icon: _googleplus_icon_png__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    twitter_icon: _twitter_icon_png__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    profile_icon: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    logo: _logo_png__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    arrow: _arrow_png__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n    logo_light: _logo_light_png__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n    blog_icon: _blog_icon_png__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n    add_icon: _add_icon_png__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n    email_icon: _email_icon_png__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n    upload_area: _upload_area_png__WEBPACK_IMPORTED_MODULE_26__[\"default\"]\n};\nconst blog_data = [\n    {\n        id: 1,\n        title: \"A detailed step by step guide to manage your lifestyle\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_1_png__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 2,\n        title: \"How to create an effective startup roadmap or ideas\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_2_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 3,\n        title: \"Learning new technology to boost your career in software\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_3_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 4,\n        title: \"Tips for getting the most out of apps and software\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_4_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 5,\n        title: \"Enhancing your skills and capturing memorable moments\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_5_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 6,\n        title: \"Maximizing returns by minimizing resources in your startup\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_6_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 7,\n        title: \"Technology for Career advancement in development\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_7_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 8,\n        title: \"A comprehensive roadmap for effective lifestyle management\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_8_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 9,\n        title: \"Achieving maximum returns with minimal resources\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_9_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 10,\n        title: \"Beyond the Ordinary: Crafting Your Exceptional Lifestyle\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_10_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 11,\n        title: \"Unveiling the Secrets of Successful Startups in Technolgy\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_11_png__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 12,\n        title: \"How to design an online Learning Platform today\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_12_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 13,\n        title: \"Tomorrow's Algorithms: Shaping the Landscape of Future AI\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_13_png__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 14,\n        title: \"Balance & Bliss: Navigating Life's Journey with Style\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_14_png__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 15,\n        title: \"Exploring the Evolution of social networking in the Future\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_15_png__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 16,\n        title: \"Shaping the Future of statup ecosystem in the world\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_16_png__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Assets/assets.js\n");

/***/ }),

/***/ "(ssr)/./Components/AdminComponents/Sidebar.jsx":
/*!************************************************!*\
  !*** ./Components/AdminComponents/Sidebar.jsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(ssr)/./Assets/assets.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n\n\n\n\n\n\n\nconst Sidebar = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showText, setShowText] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const toggleSidebar = ()=>{\n        setIsAnimating(true);\n        if (isExpanded) {\n            // When collapsing, hide text immediately\n            setShowText(false);\n            setIsExpanded(false);\n        } else {\n            // When expanding, first expand the sidebar\n            setIsExpanded(true);\n            // Then show text with animation after a small delay\n            setTimeout(()=>{\n                setShowText(true);\n            }, 150);\n        }\n        // Reset animating state after animation completes\n        setTimeout(()=>{\n            setIsAnimating(false);\n        }, 300);\n    };\n    // Check if the current path matches the link\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col bg-slate-100 ${isExpanded ? \"w-28 sm:w-80\" : \"w-20\"} transition-all duration-300 min-h-screen`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `py-3 border border-black flex items-center transition-all duration-300 ${isExpanded ? \"px-2 sm:pl-14 justify-between\" : \"px-0 justify-center\"} w-full`,\n                children: [\n                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.logo,\n                                width: 120,\n                                alt: \"Mr.Blogger\",\n                                className: \"cursor-pointer transition-all duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                lineNumber: 48,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                            lineNumber: 47,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                        lineNumber: 46,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleSidebar,\n                        className: \"p-2 hover:bg-slate-200 rounded-full transition-all ml-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"24\",\n                            height: \"24\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"none\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            className: `transform transition-transform ${!isExpanded ? \"rotate-180\" : \"\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M15 18l-6-6 6-6\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"2\",\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                lineNumber: 69,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                            lineNumber: 61,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                        lineNumber: 57,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                lineNumber: 44,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `h-full relative py-12 border border-black border-t-0 transition-all duration-300 w-full`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `${isExpanded ? \"w-[50%] sm:w-[80%]\" : \"w-[90%]\"} absolute right-0 transition-all duration-300`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/admin\",\n                            className: `flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive(\"/admin\") ? \"bg-gray-100\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-7 h-7 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"28\",\n                                        height: \"28\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z\",\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 25\n                                }, undefined),\n                                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full overflow-hidden\",\n                                    children: showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:block sidebar-text-animate whitespace-nowrap\",\n                                        children: \"Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                            lineNumber: 75,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/admin/addBlog\",\n                            className: `mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive(\"/admin/addBlog\") ? \"bg-gray-100\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-7 h-7 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.blog_icon,\n                                        alt: \"\",\n                                        width: 28\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 25\n                                }, undefined),\n                                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full overflow-hidden\",\n                                    children: showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:block sidebar-text-animate whitespace-nowrap\",\n                                        children: \"Blog Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/admin/addUser\",\n                            className: `mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive(\"/admin/addUser\") ? \"bg-gray-100\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-7 h-7 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"28\",\n                                        height: \"28\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm-9-2V7H4v3H1v2h3v3h2v-3h3v-2H6zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\",\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 25\n                                }, undefined),\n                                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full overflow-hidden\",\n                                    children: showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:block sidebar-text-animate whitespace-nowrap\",\n                                        children: \"User Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                            lineNumber: 107,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/admin/settings\",\n                            className: `mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive(\"/admin/settings\") ? \"bg-gray-100\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-7 h-7 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"28\",\n                                        height: \"28\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z\",\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 25\n                                }, undefined),\n                                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full overflow-hidden\",\n                                    children: showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:block sidebar-text-animate whitespace-nowrap\",\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                            lineNumber: 124,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/admin/feedback\",\n                            className: `mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive(\"/admin/feedback\") ? \"bg-gray-100\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-7 h-7 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"28\",\n                                        height: \"28\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z\",\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 25\n                                }, undefined),\n                                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full overflow-hidden\",\n                                    children: showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:block sidebar-text-animate whitespace-nowrap\",\n                                        children: \"Feedback\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                            lineNumber: 141,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/admin/subscriptions\",\n                            className: `mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive(\"/admin/subscriptions\") ? \"bg-gray-100\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-7 h-7 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.email_icon,\n                                        alt: \"\",\n                                        width: 28\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 25\n                                }, undefined),\n                                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full overflow-hidden\",\n                                    children: showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:block sidebar-text-animate whitespace-nowrap\",\n                                        children: \"Subscriptions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                            lineNumber: 158,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/admin/reactions\",\n                            className: `mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive(\"/admin/reactions\") ? \"bg-gray-100\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-7 h-7 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"28\",\n                                        height: \"28\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\",\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 25\n                                }, undefined),\n                                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full overflow-hidden\",\n                                    children: showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:block sidebar-text-animate whitespace-nowrap\",\n                                        children: \"Reactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                            lineNumber: 173,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/admin/traffic\",\n                            className: `mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive(\"/admin/traffic\") ? \"bg-gray-100\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 w-7 h-7 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"28\",\n                                        height: \"28\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"none\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M3 13h2v7H3v-7zm4-7h2v14H7V6zm4 3h2v11h-2V9zm4 4h2v7h-2v-7zm4-7h2v14h-2V6z\",\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 25\n                                }, undefined),\n                                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full overflow-hidden\",\n                                    children: showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:block sidebar-text-animate whitespace-nowrap\",\n                                        children: \"Traffic Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                            lineNumber: 190,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                    lineNumber: 74,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n                lineNumber: 73,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\AdminComponents\\\\Sidebar.jsx\",\n        lineNumber: 43,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/AdminComponents/Sidebar.jsx\n");

/***/ }),

/***/ "(ssr)/./Components/CookieConsent.jsx":
/*!**************************************!*\
  !*** ./Components/CookieConsent.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/cookieUtils */ \"(ssr)/./utils/cookieUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CookieConsent = ()=>{\n    const [showConsent, setShowConsent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user has already made a choice\n        const consentCookie = (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"cookie-consent\");\n        if (!consentCookie) {\n            // Set a timeout to show the consent popup after 10 seconds\n            const timer = setTimeout(()=>{\n                setShowConsent(true);\n            }, 10000) // 10 seconds\n            ;\n            return ()=>clearTimeout(timer);\n        } else if (consentCookie === \"accepted\") {\n            // If user has accepted cookies, set the necessary cookies\n            (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setAnalyticsCookies)();\n            (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setFunctionalCookies)();\n        }\n    }, []);\n    const acceptCookies = ()=>{\n        // Set cookie with 1-year expiry\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setCookie)(\"cookie-consent\", \"accepted\", {\n            expires: 365\n        });\n        // Set other cookies as needed\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setAnalyticsCookies)();\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setFunctionalCookies)();\n        setShowConsent(false);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Cookie preferences saved\");\n    };\n    const declineCookies = ()=>{\n        // Set cookie to remember user declined\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setCookie)(\"cookie-consent\", \"declined\", {\n            expires: 365\n        });\n        setShowConsent(false);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Cookies declined. Some features may be limited.\");\n    };\n    if (!showConsent) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-0 left-0 right-0 bg-white shadow-lg z-50 border-t border-gray-200 p-4 md:p-6 animate-fade-in\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row md:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"We use cookies\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm md:text-base\",\n                                children: [\n                                    'We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking \"Accept All\", you consent to our use of cookies.',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/privacy-policy\",\n                                        className: \"text-blue-600 hover:underline ml-1\",\n                                        children: \"Read our Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: declineCookies,\n                                className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 text-sm md:text-base\",\n                                children: \"Decline\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: acceptCookies,\n                                className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 text-sm md:text-base\",\n                                children: \"Accept All\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieConsent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/CookieConsent.jsx\n");

/***/ }),

/***/ "(ssr)/./Components/EmailSubscriptionPopup.jsx":
/*!***********************************************!*\
  !*** ./Components/EmailSubscriptionPopup.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst EmailSubscriptionPopup = ()=>{\n    const [showPopup, setShowPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user has already dismissed the popup\n        const hasClosedPopup = localStorage.getItem(\"emailPopupClosed\");\n        const hasSubscribed = localStorage.getItem(\"emailSubscribed\");\n        // Don't show popup if user has closed it before or already subscribed\n        if (hasClosedPopup === \"true\" || hasSubscribed === \"true\") {\n            return;\n        }\n        // Show popup after 2 minutes (120000 milliseconds)\n        const timer = setTimeout(()=>{\n            setShowPopup(true);\n        }, 120000); // 2 minutes\n        // Cleanup timer on component unmount\n        return ()=>clearTimeout(timer);\n    }, []);\n    const handleClose = ()=>{\n        setShowPopup(false);\n        // Remember that user closed the popup\n        localStorage.setItem(\"emailPopupClosed\", \"true\");\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please enter your email address\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const formData = new FormData();\n            formData.append(\"email\", email);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/email\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully subscribed to our newsletter!\");\n                setShowPopup(false);\n                setEmail(\"\");\n                // Remember that user has subscribed\n                localStorage.setItem(\"emailSubscribed\", \"true\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Subscription failed. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Subscription error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"An error occurred. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!showPopup) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-2xl max-w-md w-full relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleClose,\n                    className: \"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\",\n                    \"aria-label\": \"Close popup\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M6 18L18 6M6 6l12 12\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800 mb-2\",\n                                    children: \"SUBSCRIBE NOW\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: [\n                                        \"DON'T MISS OUT ON THE LATEST BLOG POSTS\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 54\n                                        }, undefined),\n                                        \"AND OFFERS.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: \"Be the first to get notified.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        placeholder: \"Email address\",\n                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full bg-black text-white py-3 px-6 rounded-md hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? \"SUBSCRIBING...\" : \"SUBSCRIBE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 text-center mt-4\",\n                            children: \"You can unsubscribe at any time. We respect your privacy.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EmailSubscriptionPopup);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/EmailSubscriptionPopup.jsx\n");

/***/ }),

/***/ "(ssr)/./app/admin/editBlog/[id]/page.jsx":
/*!******************************************!*\
  !*** ./app/admin/editBlog/[id]/page.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(ssr)/./Assets/assets.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst EditBlogPage = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [currentImage, setCurrentImage] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [authors, setAuthors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [likesCount, setLikesCount] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        totalViews: 0,\n        uniqueVisitors: 0\n    });\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        author: \"\",\n        authorId: \"\",\n        authorImg: \"/author_img.png\"\n    });\n    const [showBlogSelector, setShowBlogSelector] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [allBlogs, setAllBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    // Image insertion states\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [imageSearchTerm, setImageSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [imageUploadLoading, setImageUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [selectedImageFile, setSelectedImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    // Fetch categories function\n    const fetchCategories = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/categories\");\n            if (response.data.success && response.data.categories.length > 0) {\n                setCategories(response.data.categories);\n            } else {\n                console.error(\"No categories found\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load categories\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load categories\");\n        }\n    };\n    // Fetch authors function\n    const fetchAuthors = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/authors\");\n            if (response.data.success && response.data.authors.length > 0) {\n                setAuthors(response.data.authors);\n            } else {\n                console.error(\"No authors found\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load authors\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching authors:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load authors\");\n        }\n    };\n    // Fetch likes count\n    const fetchLikesCount = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(`/api/blog/likes?id=${params.id}`);\n            if (response.data.success) {\n                setLikesCount(response.data.count);\n            }\n        } catch (error) {\n            console.error(\"Error fetching likes count:\", error);\n        // Don't show error toast for this as it's not critical\n        }\n    };\n    // Fetch blog analytics\n    const fetchBlogAnalytics = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(`/api/blog/analytics?id=${params.id}`);\n            if (response.data.success) {\n                setAnalytics(response.data.analytics);\n            }\n        } catch (error) {\n            console.error(\"Error fetching blog analytics:\", error);\n        // Don't show error toast for this as it's not critical\n        }\n    };\n    // Fetch all blogs for the selector\n    const fetchAllBlogs = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/blog\");\n            setAllBlogs(response.data.blogs || []);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n        }\n    };\n    // Insert a blog mention at the cursor position\n    const insertBlogMention = (blogId, blogTitle)=>{\n        // Create the mention format: [[blogId|blogTitle]]\n        const mention = `[[${blogId}|${blogTitle}]]`;\n        // Get the textarea element\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        // Insert the mention at cursor position\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + mention + textAfter\n        });\n        // Close the selector\n        setShowBlogSelector(false);\n        // Focus back on textarea\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + mention.length, cursorPos + mention.length);\n        }, 100);\n    };\n    // Image-related functions\n    const fetchAllImages = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/images\", {\n                params: {\n                    blogId: params.id,\n                    limit: 50\n                }\n            });\n            if (response.data.success) {\n                setAllImages(response.data.images);\n            }\n        } catch (error) {\n            console.error(\"Error fetching images:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to fetch images\");\n        }\n    };\n    const handleImageUpload = async ()=>{\n        if (!selectedImageFile) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Please select an image file\");\n            return;\n        }\n        setImageUploadLoading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"image\", selectedImageFile);\n            formData.append(\"blogId\", params.id);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"/api/upload/image\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Image uploaded successfully\");\n                setSelectedImageFile(null);\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(response.data.message || \"Failed to upload image\");\n            }\n        } catch (error) {\n            console.error(\"Error uploading image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to upload image\");\n        } finally{\n            setImageUploadLoading(false);\n        }\n    };\n    const deleteImage = async (imageId, imageUrl)=>{\n        if (!window.confirm(\"Are you sure you want to delete this image? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].delete(`/api/images/${imageId}`);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Image deleted successfully\");\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(response.data.message || \"Failed to delete image\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to delete image\");\n        }\n    };\n    const insertImageReference = (imageUrl, filename)=>{\n        const imageRef = `{{image:${imageUrl}|${filename}}}`;\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + imageRef + textAfter\n        });\n        setShowImageSelector(false);\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + imageRef.length, cursorPos + imageRef.length);\n        }, 100);\n    };\n    // Fetch the blog data\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const fetchBlog = async ()=>{\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/blog\", {\n                    params: {\n                        id: params.id\n                    }\n                });\n                const blog = response.data;\n                setData({\n                    title: blog.title || \"\",\n                    description: blog.description || \"\",\n                    category: blog.category || \"Startup\",\n                    author: blog.author || \"\",\n                    authorId: blog.authorId || \"\",\n                    authorImg: blog.authorImg || \"/author_img.png\"\n                });\n                setCurrentImage(blog.image || \"\");\n                // Fetch categories and authors\n                await fetchCategories();\n                await fetchAuthors();\n                await fetchLikesCount();\n                await fetchBlogAnalytics();\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error fetching blog:\", error);\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load blog data\");\n                router.push(\"/admin/blogList\");\n            }\n        };\n        fetchBlog();\n    }, [\n        params.id,\n        router\n    ]);\n    const onChangeHandler = (event)=>{\n        const name = event.target.name;\n        const value = event.target.value;\n        if (name === \"authorId\") {\n            // Find the selected author\n            const selectedAuthor = authors.find((author)=>author._id === value);\n            if (selectedAuthor) {\n                setData((data)=>({\n                        ...data,\n                        author: selectedAuthor.name,\n                        authorId: selectedAuthor._id,\n                        authorImg: selectedAuthor.image || \"/author_img.png\"\n                    }));\n            }\n        } else {\n            setData((data)=>({\n                    ...data,\n                    [name]: value\n                }));\n        }\n    };\n    const onSubmitHandler = async (e)=>{\n        e.preventDefault();\n        try {\n            const formData = new FormData();\n            formData.append(\"id\", params.id);\n            formData.append(\"title\", data.title);\n            formData.append(\"description\", data.description);\n            formData.append(\"category\", data.category);\n            formData.append(\"author\", data.author);\n            formData.append(\"authorId\", data.authorId);\n            formData.append(\"authorImg\", data.authorImg);\n            // Only append image if a new one was selected\n            if (image) {\n                formData.append(\"image\", image);\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].put(\"/api/blog\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(response.data.msg || \"Blog updated successfully\");\n                router.push(\"/admin/blogList\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(response.data.msg || \"Error updating blog\");\n            }\n        } catch (error) {\n            console.error(\"Error updating blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to update blog\");\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"pt-5 px-5 sm:pt-12 sm:pl-16\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Loading blog data...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                lineNumber: 314,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n            lineNumber: 313,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: onSubmitHandler,\n            className: \"pt-5 px-5 sm:pt-12 sm:pl-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"Edit Blog\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 323,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            className: \"text-red-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: likesCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"likes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            className: \"text-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: analytics.totalViews\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"views\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 325,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 322,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl\",\n                    children: \"Current thumbnail\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 361,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: currentImage,\n                        width: 200,\n                        height: 120,\n                        alt: \"Current thumbnail\",\n                        className: \"border border-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                        lineNumber: 363,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 362,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl\",\n                    children: \"Upload new thumbnail (optional)\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 372,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    htmlFor: \"image\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        className: \"mt-4 cursor-pointer\",\n                        src: !image ? _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.upload_area : URL.createObjectURL(image),\n                        width: 140,\n                        height: 70,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                        lineNumber: 374,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 373,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    onChange: (e)=>setImage(e.target.files[0]),\n                    type: \"file\",\n                    id: \"image\",\n                    hidden: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 382,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl mt-4\",\n                    children: \"Blog title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 389,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    name: \"title\",\n                    onChange: onChangeHandler,\n                    value: data.title,\n                    className: \"w-full sm:w-[500px] mt-4 px-4 py-3 border\",\n                    type: \"text\",\n                    placeholder: \"Type here\",\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 390,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl\",\n                            children: \"Blog Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 401,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"blog-description\",\n                                name: \"description\",\n                                onChange: onChangeHandler,\n                                value: data.description,\n                                className: \"w-full sm:w-[500px] px-4 py-3 border\",\n                                placeholder: \"Write content here\",\n                                rows: 6,\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                lineNumber: 403,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 402,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 flex items-center flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        fetchAllBlogs();\n                                        setShowBlogSelector(true);\n                                    },\n                                    className: \"text-sm flex items-center text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Mention another blog\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        fetchAllImages();\n                                        setShowImageSelector(true);\n                                    },\n                                    className: \"text-sm flex items-center text-green-600 hover:text-green-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Insert image\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Formats: [[blogId|blogTitle]] | \",\n                                            `{{image:url|filename}}`\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 414,\n                            columnNumber: 21\n                        }, undefined),\n                        showBlogSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Select a blog to mention\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowBlogSelector(false),\n                                                className: \"text-gray-500 hover:text-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search blogs...\",\n                                        className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"divide-y\",\n                                        children: allBlogs.filter((blog)=>blog.title.toLowerCase().includes(searchTerm.toLowerCase())).map((blog)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center\",\n                                                onClick: ()=>insertBlogMention(blog._id, blog.title),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 relative mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            src: blog.image,\n                                                            alt: blog.title,\n                                                            fill: true,\n                                                            className: \"object-cover rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium\",\n                                                                children: blog.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: blog.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                ]\n                                            }, blog._id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 45\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                lineNumber: 451,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 450,\n                            columnNumber: 25\n                        }, undefined),\n                        showImageSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Insert Image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowImageSelector(false),\n                                                className: \"text-gray-500 hover:text-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 p-4 border rounded-lg bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3\",\n                                                children: \"Upload New Image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        onChange: (e)=>setSelectedImageFile(e.target.files[0]),\n                                                        className: \"flex-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleImageUpload,\n                                                        disabled: !selectedImageFile || imageUploadLoading,\n                                                        className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\",\n                                                        children: imageUploadLoading ? \"Uploading...\" : \"Upload\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search images...\",\n                                        className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                        value: imageSearchTerm,\n                                        onChange: (e)=>setImageSearchTerm(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                        children: allImages.filter((image)=>image.filename.toLowerCase().includes(imageSearchTerm.toLowerCase())).map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"aspect-square relative mb-2\",\n                                                        onClick: ()=>insertImageReference(image.url, image.filename),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: image.url,\n                                                                alt: image.filename,\n                                                                fill: true,\n                                                                className: \"object-cover rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    deleteImage(image._id, image.url);\n                                                                },\n                                                                className: \"absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                title: \"Delete image\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-4 w-4\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    stroke: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>insertImageReference(image.url, image.filename),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 truncate\",\n                                                                children: image.filename\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: new Date(image.uploadDate).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                ]\n                                            }, image._id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 45\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    allImages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: \"No images found. Upload your first image above.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                lineNumber: 503,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 502,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 400,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl mt-4\",\n                    children: \"Blog category\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 597,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                    name: \"category\",\n                    onChange: onChangeHandler,\n                    value: data.category,\n                    className: \"w-40 mt-4 px-4 py-3 border text-gray-500\",\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                            value: category.name,\n                            children: category.name\n                        }, category._id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 605,\n                            columnNumber: 25\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 598,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl mt-4\",\n                    children: \"Blog author\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 611,\n                    columnNumber: 17\n                }, undefined),\n                authors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            name: \"authorId\",\n                            onChange: onChangeHandler,\n                            value: data.authorId,\n                            className: \"w-full sm:w-40 px-4 py-3 border text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select an author\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 29\n                                }, undefined),\n                                authors.map((author)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: author._id,\n                                        children: author.name\n                                    }, author._id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 33\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 614,\n                            columnNumber: 25\n                        }, undefined),\n                        data.authorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mt-2 sm:mt-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: data.authorImg,\n                                    alt: data.author,\n                                    className: \"w-10 h-10 rounded-full object-cover border border-gray-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium\",\n                                    children: data.author\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 629,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 613,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-500\",\n                            children: \"No authors available. Please add authors in Settings.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 641,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            name: \"author\",\n                            onChange: onChangeHandler,\n                            value: data.author,\n                            className: \"w-full sm:w-[500px] mt-2 px-4 py-3 border\",\n                            type: \"text\",\n                            placeholder: \"Author name\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 642,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 640,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"mt-8 w-40 h-12 bg-black text-white\",\n                            children: \"UPDATE\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 655,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>router.push(\"/admin/blogList\"),\n                            className: \"mt-8 w-40 h-12 border border-black\",\n                            children: \"CANCEL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 662,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 654,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n            lineNumber: 321,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EditBlogPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/admin/editBlog/[id]/page.jsx\n");

/***/ }),

/***/ "(ssr)/./app/admin/layout.jsx":
/*!******************************!*\
  !*** ./app/admin/layout.jsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(ssr)/./Assets/assets.js\");\n/* harmony import */ var _Components_AdminComponents_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/Components/AdminComponents/Sidebar */ \"(ssr)/./Components/AdminComponents/Sidebar.jsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(ssr)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Layout({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(true);\n    const [userProfilePicture, setUserProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"/default_profile.png\");\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"Admin\");\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        // Check if user is authenticated and has admin role\n        const authToken = localStorage.getItem(\"authToken\");\n        const userRole = localStorage.getItem(\"userRole\");\n        const profilePicture = localStorage.getItem(\"userProfilePicture\");\n        const storedUserName = localStorage.getItem(\"userName\");\n        if (!authToken || userRole !== \"admin\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"You must be logged in as an admin to access this page\");\n            router.push(\"/\");\n        } else {\n            setIsAuthenticated(true);\n            if (profilePicture) {\n                setUserProfilePicture(profilePicture);\n            }\n            if (storedUserName) {\n                setUserName(storedUserName);\n            }\n        }\n        setIsLoading(false);\n    }, [\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        // Listen for storage events to update profile picture when changed\n        const handleStorageChange = ()=>{\n            const profilePicture = localStorage.getItem(\"userProfilePicture\");\n            const storedUserName = localStorage.getItem(\"userName\");\n            if (profilePicture) {\n                setUserProfilePicture(profilePicture);\n            }\n            if (storedUserName) {\n                setUserName(storedUserName);\n            }\n        };\n        // Listen for custom profile update event\n        const handleProfileUpdate = (event)=>{\n            const { name, profilePicture } = event.detail;\n            if (name) {\n                setUserName(name);\n            }\n            if (profilePicture) {\n                setUserProfilePicture(profilePicture);\n            }\n        };\n        window.addEventListener(\"storage\", handleStorageChange);\n        window.addEventListener(\"profileUpdate\", handleProfileUpdate);\n        // Also check on mount\n        const profilePicture = localStorage.getItem(\"userProfilePicture\");\n        const storedUserName = localStorage.getItem(\"userName\");\n        if (profilePicture) {\n            setUserProfilePicture(profilePicture);\n        }\n        if (storedUserName) {\n            setUserName(storedUserName);\n        }\n        return ()=>{\n            window.removeEventListener(\"storage\", handleStorageChange);\n            window.removeEventListener(\"profileUpdate\", handleProfileUpdate);\n        };\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n                lineNumber: 93,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n            lineNumber: 92,\n            columnNumber: 13\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Don't render anything while redirecting\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_4__.ToastContainer, {\n                    theme: \"dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n                    lineNumber: 105,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AdminComponents_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n                    lineNumber: 106,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between w-full py-3 max-h-[60px] px-12 border-b border-black\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium\",\n                                    children: \"Admin Panel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                        href: \"/admin/profile\",\n                                        className: \"flex items-center gap-2 text-sm py-1 px-3 border border-black shadow-[-3px_3px_0px_#000000] hover:bg-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: userName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                src: userProfilePicture,\n                                                width: 24,\n                                                height: 24,\n                                                alt: \"Profile\",\n                                                className: \"rounded-full object-cover w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n                            lineNumber: 108,\n                            columnNumber: 21\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n                    lineNumber: 107,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\layout.jsx\",\n            lineNumber: 104,\n            columnNumber: 13\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/admin/layout.jsx\n");

/***/ }),

/***/ "(ssr)/./utils/cookieUtils.js":
/*!******************************!*\
  !*** ./utils/cookieUtils.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCookie: () => (/* binding */ getCookie),\n/* harmony export */   hasAcceptedCookies: () => (/* binding */ hasAcceptedCookies),\n/* harmony export */   removeCookie: () => (/* binding */ removeCookie),\n/* harmony export */   setAnalyticsCookies: () => (/* binding */ setAnalyticsCookies),\n/* harmony export */   setCookie: () => (/* binding */ setCookie),\n/* harmony export */   setFunctionalCookies: () => (/* binding */ setFunctionalCookies)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n// Set a cookie\nconst setCookie = (name, value, options = {})=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(name, value, options);\n};\n// Get a cookie\nconst getCookie = (name)=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n};\n// Remove a cookie\nconst removeCookie = (name)=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(name);\n};\n// Check if user has accepted cookies\nconst hasAcceptedCookies = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"cookie-consent\") === \"accepted\";\n};\n// Set analytics cookies (only if user has accepted)\nconst setAnalyticsCookies = ()=>{\n    if (hasAcceptedCookies()) {\n    // Set analytics cookies here\n    // Example: Cookies.set('_ga', 'GA1.2.123456789.1234567890', { expires: 365 })\n    }\n};\n// Set functional cookies (only if user has accepted)\nconst setFunctionalCookies = ()=>{\n    if (hasAcceptedCookies()) {\n    // Set functional cookies here\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/cookieUtils.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1db334ffb0c5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vYXBwL2dsb2JhbHMuY3NzP2M5ZWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZGIzMzRmZmIwYzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./Components/CookieConsent.jsx":
/*!**************************************!*\
  !*** ./Components/CookieConsent.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\Components\CookieConsent.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./Components/EmailSubscriptionPopup.jsx":
/*!***********************************************!*\
  !*** ./Components/EmailSubscriptionPopup.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\Components\EmailSubscriptionPopup.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/admin/editBlog/[id]/page.jsx":
/*!******************************************!*\
  !*** ./app/admin/editBlog/[id]/page.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\editBlog\[id]\page.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/admin/layout.jsx":
/*!******************************!*\
  !*** ./app/admin/layout.jsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\admin\layout.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.jsx":
/*!************************!*\
  !*** ./app/layout.jsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.jsx\",\"import\":\"Outfit\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"outfit\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.jsx\\\",\\\"import\\\":\\\"Outfit\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"outfit\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _Components_CookieConsent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/Components/CookieConsent */ \"(rsc)/./Components/CookieConsent.jsx\");\n/* harmony import */ var _Components_EmailSubscriptionPopup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/Components/EmailSubscriptionPopup */ \"(rsc)/./Components/EmailSubscriptionPopup.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(rsc)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(rsc)/./node_modules/react-toastify/dist/ReactToastify.css\");\n\n\n\n\n\n\n\n// In production, use the pre-built CSS file\nconst isProd = \"development\" === \"production\";\nconst cssPath = isProd ? \"/build/tailwind.css\" : \"./globals.css\";\nconst metadata = {\n    title: \"Mr.Blogger\",\n    description: \"A blog platform by Mr.Blogger\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: isProd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: cssPath\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                    lineNumber: 23,\n                    columnNumber: 20\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_6___default().className),\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_CookieConsent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_EmailSubscriptionPopup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_4__.ToastContainer, {\n                        position: \"top-center\",\n                        autoClose: 3000\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.jsx\n");

/***/ }),

/***/ "(ssr)/./Assets/add_icon.png":
/*!*****************************!*\
  !*** ./Assets/add_icon.png ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/add_icon.17426346.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fadd_icon.17426346.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYWRkX2ljb24ucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLGtNQUFrTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYWRkX2ljb24ucG5nP2EzMDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2FkZF9pY29uLjE3NDI2MzQ2LnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFkZF9pY29uLjE3NDI2MzQ2LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/add_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/arrow.png":
/*!**************************!*\
  !*** ./Assets/arrow.png ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/arrow.35bdbbc1.png\",\"height\":16,\"width\":18,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Farrow.35bdbbc1.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":7});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYXJyb3cucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYXJyb3cucG5nPzYxZjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Fycm93LjM1YmRiYmMxLnBuZ1wiLFwiaGVpZ2h0XCI6MTYsXCJ3aWR0aFwiOjE4LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFycm93LjM1YmRiYmMxLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo3fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/arrow.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_icon.png":
/*!******************************!*\
  !*** ./Assets/blog_icon.png ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_icon.6cf97bbc.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_icon.6cf97bbc.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxvTUFBb00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL2Jsb2dfaWNvbi5wbmc/MjJlYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvYmxvZ19pY29uLjZjZjk3YmJjLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfaWNvbi42Y2Y5N2JiYy5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_1.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_1.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_1.4406e300.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_1.4406e300.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18xLnBuZz9lMTk3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18xLjQ0MDZlMzAwLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEuNDQwNmUzMDAucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_1.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_10.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_10.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_10.b87908bf.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_10.b87908bf.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTAucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTAucG5nPzA0NzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEwLmI4NzkwOGJmLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEwLmI4NzkwOGJmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_10.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_11.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_11.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_11.ffa40298.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_11.ffa40298.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTEucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTEucG5nPzAxMDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzExLmZmYTQwMjk4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzExLmZmYTQwMjk4LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_11.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_12.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_12.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_12.e5886225.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_12.e5886225.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTIucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTIucG5nPzQ3ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEyLmU1ODg2MjI1LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEyLmU1ODg2MjI1LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_12.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_13.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_13.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_13.d4a89f0e.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_13.d4a89f0e.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTMucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTMucG5nPzJjNDgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEzLmQ0YTg5ZjBlLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEzLmQ0YTg5ZjBlLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_13.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_14.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_14.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_14.0d239c78.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_14.0d239c78.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTQucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTQucG5nPzMxNzciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE0LjBkMjM5Yzc4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE0LjBkMjM5Yzc4LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_14.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_15.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_15.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_15.1d85ee32.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_15.1d85ee32.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTUucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTUucG5nPzYyOGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE1LjFkODVlZTMyLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE1LjFkODVlZTMyLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_15.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_16.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_16.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_16.ffb19842.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_16.ffb19842.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTYucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTYucG5nPzU5NGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE2LmZmYjE5ODQyLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE2LmZmYjE5ODQyLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_16.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_2.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_2.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_2.b986ae66.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_2.b986ae66.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18yLnBuZz9mZWI1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18yLmI5ODZhZTY2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzIuYjk4NmFlNjYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_2.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_3.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_3.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_3.5e7fff80.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_3.5e7fff80.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMy5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18zLnBuZz9kZWRlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18zLjVlN2ZmZjgwLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzMuNWU3ZmZmODAucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_3.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_4.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_4.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_4.86f96556.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_4.86f96556.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY180LnBuZz8yMGEyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY180Ljg2Zjk2NTU2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzQuODZmOTY1NTYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_4.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_5.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_5.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_5.144896ce.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_5.144896ce.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY181LnBuZz82YjE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY181LjE0NDg5NmNlLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzUuMTQ0ODk2Y2UucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_5.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_6.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_6.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_6.b530ea03.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_6.b530ea03.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY182LnBuZz84ZWQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY182LmI1MzBlYTAzLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzYuYjUzMGVhMDMucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_6.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_7.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_7.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_7.3dcf8c5f.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_7.3dcf8c5f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNy5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY183LnBuZz9mNGJjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY183LjNkY2Y4YzVmLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzcuM2RjZjhjNWYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_7.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_8.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_8.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_8.50101226.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_8.50101226.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfOC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY184LnBuZz85MGQ2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY184LjUwMTAxMjI2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzguNTAxMDEyMjYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_8.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_9.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_9.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_9.56aa9ce8.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_9.56aa9ce8.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfOS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY185LnBuZz9lODQyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY185LjU2YWE5Y2U4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzkuNTZhYTljZTgucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_9.png\n");

/***/ }),

/***/ "(ssr)/./Assets/email_icon.png":
/*!*******************************!*\
  !*** ./Assets/email_icon.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/email_icon.4caec7c6.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Femail_icon.4caec7c6.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZW1haWxfaWNvbi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsc01BQXNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9lbWFpbF9pY29uLnBuZz82MmY1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9lbWFpbF9pY29uLjRjYWVjN2M2LnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmVtYWlsX2ljb24uNGNhZWM3YzYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/email_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/facebook_icon.png":
/*!**********************************!*\
  !*** ./Assets/facebook_icon.png ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/facebook_icon.cbcfc36d.png\",\"height\":58,\"width\":58,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffacebook_icon.cbcfc36d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZmFjZWJvb2tfaWNvbi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsNE1BQTRNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9mYWNlYm9va19pY29uLnBuZz85ZDVmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mYWNlYm9va19pY29uLmNiY2ZjMzZkLnBuZ1wiLFwiaGVpZ2h0XCI6NTgsXCJ3aWR0aFwiOjU4LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmZhY2Vib29rX2ljb24uY2JjZmMzNmQucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/facebook_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/googleplus_icon.png":
/*!************************************!*\
  !*** ./Assets/googleplus_icon.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/googleplus_icon.15e2de32.png\",\"height\":59,\"width\":59,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fgoogleplus_icon.15e2de32.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZ29vZ2xlcGx1c19pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxnTkFBZ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL2dvb2dsZXBsdXNfaWNvbi5wbmc/OWM1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZ29vZ2xlcGx1c19pY29uLjE1ZTJkZTMyLnBuZ1wiLFwiaGVpZ2h0XCI6NTksXCJ3aWR0aFwiOjU5LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmdvb2dsZXBsdXNfaWNvbi4xNWUyZGUzMi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/googleplus_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/logo.png":
/*!*************************!*\
  !*** ./Assets/logo.png ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo.c649e147.png\",\"height\":53,\"width\":186,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.c649e147.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":2});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvbG9nby5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsMkxBQTJMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9sb2dvLnBuZz9jOWE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9sb2dvLmM2NDllMTQ3LnBuZ1wiLFwiaGVpZ2h0XCI6NTMsXCJ3aWR0aFwiOjE4NixcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvLmM2NDllMTQ3LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjoyfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/logo.png\n");

/***/ }),

/***/ "(ssr)/./Assets/logo_light.png":
/*!*******************************!*\
  !*** ./Assets/logo_light.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo_light.9ce1f99e.png\",\"height\":55,\"width\":201,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo_light.9ce1f99e.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":2});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvbG9nb19saWdodC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsdU1BQXVNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9sb2dvX2xpZ2h0LnBuZz83NWI0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9sb2dvX2xpZ2h0LjljZTFmOTllLnBuZ1wiLFwiaGVpZ2h0XCI6NTUsXCJ3aWR0aFwiOjIwMSxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvX2xpZ2h0LjljZTFmOTllLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjoyfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/logo_light.png\n");

/***/ }),

/***/ "(ssr)/./Assets/profile_icon.png":
/*!*********************************!*\
  !*** ./Assets/profile_icon.png ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/profile_icon.fa2679c4.png\",\"height\":92,\"width\":92,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprofile_icon.fa2679c4.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvcHJvZmlsZV9pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywwTUFBME0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL3Byb2ZpbGVfaWNvbi5wbmc/NmI3NyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvcHJvZmlsZV9pY29uLmZhMjY3OWM0LnBuZ1wiLFwiaGVpZ2h0XCI6OTIsXCJ3aWR0aFwiOjkyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnByb2ZpbGVfaWNvbi5mYTI2NzljNC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/profile_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/twitter_icon.png":
/*!*********************************!*\
  !*** ./Assets/twitter_icon.png ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/twitter_icon.0d1dc581.png\",\"height\":59,\"width\":59,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ftwitter_icon.0d1dc581.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvdHdpdHRlcl9pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywwTUFBME0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL3R3aXR0ZXJfaWNvbi5wbmc/NDI5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvdHdpdHRlcl9pY29uLjBkMWRjNTgxLnBuZ1wiLFwiaGVpZ2h0XCI6NTksXCJ3aWR0aFwiOjU5LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnR3aXR0ZXJfaWNvbi4wZDFkYzU4MS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/twitter_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/upload_area.png":
/*!********************************!*\
  !*** ./Assets/upload_area.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/upload_area.1ee5fe3d.png\",\"height\":140,\"width\":240,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fupload_area.1ee5fe3d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvdXBsb2FkX2FyZWEucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDBNQUEwTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvdXBsb2FkX2FyZWEucG5nP2RhZTMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL3VwbG9hZF9hcmVhLjFlZTVmZTNkLnBuZ1wiLFwiaGVpZ2h0XCI6MTQwLFwid2lkdGhcIjoyNDAsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGdXBsb2FkX2FyZWEuMWVlNWZlM2QucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/upload_area.png\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9hcHAvZmF2aWNvbi5pY28/YWVmZiJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/ms","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/react-toastify","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/supports-color","vendor-chunks/delayed-stream","vendor-chunks/clsx","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&page=%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&appPaths=%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fadmin%2FeditBlog%2F%5Bid%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();