"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./Components/BlogList.jsx":
/*!*********************************!*\
  !*** ./Components/BlogList.jsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _BlogItem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BlogItem */ \"(app-pages-browser)/./Components/BlogItem.jsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst BLOGS_PER_PAGE = 12;\nconst BlogList = (param)=>{\n    let { searchTerm = \"\" } = param;\n    _s();\n    const [menu, setMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [blogs, setBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showCategoryDropdown, setShowCategoryDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fetch data\n    const fetchBlogs = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/blog\");\n            if (response.data.blogs) {\n                setBlogs(response.data.blogs);\n            } else {\n                // Handle case where API returns error but with 200 status\n                setBlogs([]);\n            }\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n            setBlogs([]); // Set empty array on error\n            setLoading(false);\n        }\n    };\n    const fetchCategories = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/categories\");\n            if (response.data.success && response.data.categories.length > 0) {\n                setCategories(response.data.categories);\n            } else {\n                // Fallback to default categories if none found\n                setCategories([\n                    {\n                        _id: \"1\",\n                        name: \"Startup\"\n                    },\n                    {\n                        _id: \"2\",\n                        name: \"Technology\"\n                    },\n                    {\n                        _id: \"3\",\n                        name: \"Lifestyle\"\n                    }\n                ]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n            // Fallback to default categories on error\n            setCategories([\n                {\n                    _id: \"1\",\n                    name: \"Startup\"\n                },\n                {\n                    _id: \"2\",\n                    name: \"Technology\"\n                },\n                {\n                    _id: \"3\",\n                    name: \"Lifestyle\"\n                }\n            ]);\n        }\n    };\n    // Set mounted state to prevent hydration mismatch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMounted(true);\n        fetchBlogs();\n        fetchCategories();\n    }, []);\n    // Reset to first page when menu changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setCurrentPage(1);\n    }, [\n        menu\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isMounted) return;\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                setShowCategoryDropdown(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isMounted\n    ]);\n    // Filter blogs by category and search term\n    const filteredBlogs = blogs.filter((item)=>{\n        const matchesCategory = menu === \"All\" ? true : item.category === menu;\n        const matchesSearch = searchTerm.trim() === \"\" ? true : item.title.toLowerCase().includes(searchTerm.toLowerCase()) || item.description.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesCategory && matchesSearch;\n    });\n    const totalPages = Math.ceil(filteredBlogs.length / BLOGS_PER_PAGE);\n    const startIdx = (currentPage - 1) * BLOGS_PER_PAGE;\n    const currentBlogs = filteredBlogs.slice(startIdx, startIdx + BLOGS_PER_PAGE);\n    const handleCategorySelect = (categoryName)=>{\n        setMenu(categoryName);\n        setShowCategoryDropdown(false);\n    };\n    // Return a loading state until client-side hydration is complete\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-10\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n            lineNumber: 107,\n            columnNumber: 16\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center my-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setMenu(\"All\"),\n                            className: \"py-2 px-6 rounded-md transition-colors \".concat(menu === \"All\" ? \"bg-black text-white\" : \"bg-gray-100 hover:bg-gray-200\"),\n                            children: \"All\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                            lineNumber: 114,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            ref: dropdownRef,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCategoryDropdown(!showCategoryDropdown),\n                                    className: \"py-2 px-6 rounded-md flex items-center gap-2 transition-colors \".concat(menu !== \"All\" ? \"bg-black text-white\" : \"bg-gray-100 hover:bg-gray-200\"),\n                                    children: [\n                                        menu !== \"All\" ? menu : \"Categories\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 transition-transform \".concat(showCategoryDropdown ? \"rotate-180\" : \"\"),\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 9l-7 7-7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 25\n                                }, undefined),\n                                showCategoryDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute z-10 mt-1 w-48 bg-white rounded-md shadow-lg py-1 max-h-60 overflow-auto\",\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleCategorySelect(category.name),\n                                            className: \"block w-full text-left px-4 py-2 text-sm \".concat(menu === category.name ? \"bg-gray-100 font-medium\" : \"hover:bg-gray-50\"),\n                                            children: category.name\n                                        }, category._id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 37\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                            lineNumber: 125,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                    lineNumber: 113,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                lineNumber: 112,\n                columnNumber: 13\n            }, undefined),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-10\",\n                children: \"Loading blogs...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                lineNumber: 168,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-32 xl:mx-24\",\n                        children: currentBlogs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BlogItem__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                id: item._id,\n                                image: item.image,\n                                title: item.title,\n                                description: item.description,\n                                category: item.category\n                            }, item._id || index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                lineNumber: 173,\n                                columnNumber: 29\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                        lineNumber: 171,\n                        columnNumber: 21\n                    }, undefined),\n                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-0 mb-8 gap-2\",\n                        children: Array.from({\n                            length: totalPages\n                        }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentPage(i + 1),\n                                className: \"px-4 py-2 border border-black rounded \".concat(currentPage === i + 1 ? \"bg-black text-white\" : \"bg-white text-black hover:bg-gray-100\"),\n                                children: i + 1\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                lineNumber: 187,\n                                columnNumber: 33\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                        lineNumber: 185,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n        lineNumber: 111,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BlogList, \"+VDykMtyZiTkTzBYBSqs5U5PaSE=\");\n_c = BlogList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BlogList);\nvar _c;\n$RefreshReg$(_c, \"BlogList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/BlogList.jsx\n"));

/***/ })

});