"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"32690c1056dc\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzP2NmNDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzMjY5MGMxMDU2ZGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./Components/EmailSubscriptionPopup.jsx":
/*!***********************************************!*\
  !*** ./Components/EmailSubscriptionPopup.jsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst EmailSubscriptionPopup = ()=>{\n    _s();\n    const [showPopup, setShowPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user has already subscribed (permanent dismissal)\n        const hasSubscribed = localStorage.getItem(\"emailSubscribed\");\n        if (hasSubscribed === \"true\") {\n            return;\n        }\n        // Check if user has closed the popup recently\n        const lastClosedTime = localStorage.getItem(\"emailPopupLastClosed\");\n        const now = Date.now();\n        // If popup was closed less than 10 minutes ago, don't show it\n        if (lastClosedTime && now - parseInt(lastClosedTime) < 600000) {\n            return;\n        }\n        // Show popup after 2 minutes (120000 milliseconds)\n        const timer = setTimeout(()=>{\n            setShowPopup(true);\n        }, 120000); // 2 minutes\n        // Cleanup timer on component unmount\n        return ()=>clearTimeout(timer);\n    }, []);\n    const handleClose = ()=>{\n        setShowPopup(false);\n        // Remember when user closed the popup (timestamp)\n        localStorage.setItem(\"emailPopupLastClosed\", Date.now().toString());\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please enter your email address\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const formData = new FormData();\n            formData.append(\"email\", email);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/email\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully subscribed to our newsletter!\");\n                setShowPopup(false);\n                setEmail(\"\");\n                // Remember that user has subscribed\n                localStorage.setItem(\"emailSubscribed\", \"true\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Subscription failed. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Subscription error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"An error occurred. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!showPopup) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-2xl max-w-md w-full relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleClose,\n                    className: \"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\",\n                    \"aria-label\": \"Close popup\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M6 18L18 6M6 6l12 12\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800 mb-2\",\n                                    children: \"SUBSCRIBE NOW\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: [\n                                        \"DON'T MISS OUT ON THE LATEST BLOG POSTS\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 54\n                                        }, undefined),\n                                        \"AND OFFERS.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: \"Be the first to get notified.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        placeholder: \"Email address\",\n                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full bg-black text-white py-3 px-6 rounded-md hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? \"SUBSCRIBING...\" : \"SUBSCRIBE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 text-center mt-4\",\n                            children: \"You can unsubscribe at any time. We respect your privacy.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EmailSubscriptionPopup, \"xVHiayOFpKMUBGwoMbN0IoNfq9A=\");\n_c = EmailSubscriptionPopup;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EmailSubscriptionPopup);\nvar _c;\n$RefreshReg$(_c, \"EmailSubscriptionPopup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/EmailSubscriptionPopup.jsx\n"));

/***/ })

});