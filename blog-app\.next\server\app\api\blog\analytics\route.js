"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/blog/analytics/route";
exports.ids = ["app/api/blog/analytics/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Fanalytics%2Froute&page=%2Fapi%2Fblog%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Fanalytics%2Froute&page=%2Fapi%2Fblog%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_blog_analytics_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/blog/analytics/route.js */ \"(rsc)/./app/api/blog/analytics/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/blog/analytics/route\",\n        pathname: \"/api/blog/analytics\",\n        filename: \"route\",\n        bundlePath: \"app/api/blog/analytics/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\blog\\\\analytics\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_blog_analytics_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/blog/analytics/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Fanalytics%2Froute&page=%2Fapi%2Fblog%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/blog/analytics/route.js":
/*!*****************************************!*\
  !*** ./app/api/blog/analytics/route.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/AnalyticsModel */ \"(rsc)/./lib/models/AnalyticsModel.js\");\n/* harmony import */ var _utils_authUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/authUtils */ \"(rsc)/./utils/authUtils.js\");\n\n\n\n\nasync function GET(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        // Verify authentication\n        const userData = (0,_utils_authUtils__WEBPACK_IMPORTED_MODULE_3__.getUserFromToken)(request);\n        if (!userData) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const blogId = searchParams.get(\"id\");\n        if (!blogId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Blog ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get total views for this blog\n        const totalViews = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments({\n            blogId\n        });\n        // Get unique visitors for this blog\n        const uniqueVisitors = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].distinct(\"ipHash\", {\n            blogId\n        }).then((ips)=>ips.length);\n        // Get views over time (daily)\n        const viewsOverTime = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $match: {\n                    blogId: blogId\n                }\n            },\n            {\n                $group: {\n                    _id: {\n                        $dateToString: {\n                            format: \"%Y-%m-%d\",\n                            date: \"$timestamp\"\n                        }\n                    },\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    \"_id\": 1\n                }\n            }\n        ]);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            analytics: {\n                totalViews,\n                uniqueVisitors,\n                viewsOverTime\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching blog analytics:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to fetch blog analytics\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/blog/analytics/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState) {\n            console.log(\"DB Already Connected\");\n            return;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, {\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000\n        });\n        console.log(\"DB Connected\");\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n    // Don't throw the error to prevent 500 responses\n    // The API will handle the case where DB is not connected\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/AnalyticsModel.js":
/*!**************************************!*\
  !*** ./lib/models/AnalyticsModel.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst AnalyticsSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    // Page or post being viewed\n    path: {\n        type: String,\n        required: true,\n        index: true\n    },\n    // Type of content (blog, page, etc.)\n    contentType: {\n        type: String,\n        required: true,\n        enum: [\n            \"blog\",\n            \"page\",\n            \"home\",\n            \"other\"\n        ],\n        default: \"other\"\n    },\n    // Associated blog ID if applicable\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"blogs\",\n        index: true\n    },\n    // User ID if logged in\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"users\",\n        index: true\n    },\n    // IP address (hashed for privacy)\n    ipHash: {\n        type: String,\n        index: true\n    },\n    // User agent info\n    userAgent: String,\n    // Referrer URL\n    referrer: String,\n    // Timestamp\n    timestamp: {\n        type: Date,\n        default: Date.now,\n        index: true\n    }\n});\n// Create compound indexes for efficient querying\nAnalyticsSchema.index({\n    path: 1,\n    timestamp: 1\n});\nAnalyticsSchema.index({\n    contentType: 1,\n    timestamp: 1\n});\nAnalyticsSchema.index({\n    blogId: 1,\n    timestamp: 1\n});\n// Check if model exists before creating\nconst AnalyticsModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).analytics || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"analytics\", AnalyticsSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AnalyticsModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/AnalyticsModel.js\n");

/***/ }),

/***/ "(rsc)/./utils/authUtils.js":
/*!****************************!*\
  !*** ./utils/authUtils.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getUserFromToken: () => (/* binding */ getUserFromToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n\n// Get user data from JWT token in request headers\nconst getUserFromToken = (request)=>{\n    try {\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return null;\n        }\n        const token = authHeader.split(\" \")[1];\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, process.env.JWT_SECRET || \"your-secret-key\");\n        return {\n            userId: decoded.userId,\n            email: decoded.email,\n            role: decoded.role\n        };\n    } catch (error) {\n        console.error(\"Error verifying token:\", error);\n        return null;\n    }\n};\n// Generate JWT token\nconst generateToken = (user)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n        userId: user._id,\n        email: user.email,\n        role: user.role\n    }, process.env.JWT_SECRET || \"your-secret-key\", {\n        expiresIn: \"7d\"\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi91dGlscy9hdXRoVXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErQjtBQUUvQixrREFBa0Q7QUFDM0MsTUFBTUMsbUJBQW1CLENBQUNDO0lBQy9CLElBQUk7UUFDRixNQUFNQyxhQUFhRCxRQUFRRSxPQUFPLENBQUNDLEdBQUcsQ0FBQztRQUN2QyxJQUFJLENBQUNGLGNBQWMsQ0FBQ0EsV0FBV0csVUFBVSxDQUFDLFlBQVk7WUFDcEQsT0FBTztRQUNUO1FBRUEsTUFBTUMsUUFBUUosV0FBV0ssS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQ3RDLE1BQU1DLFVBQVVULDBEQUFVLENBQUNPLE9BQU9JLFFBQVFDLEdBQUcsQ0FBQ0MsVUFBVSxJQUFJO1FBRTVELE9BQU87WUFDTEMsUUFBUUwsUUFBUUssTUFBTTtZQUN0QkMsT0FBT04sUUFBUU0sS0FBSztZQUNwQkMsTUFBTVAsUUFBUU8sSUFBSTtRQUNwQjtJQUNGLEVBQUUsT0FBT0MsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtRQUN4QyxPQUFPO0lBQ1Q7QUFDRixFQUFFO0FBRUYscUJBQXFCO0FBQ2QsTUFBTUUsZ0JBQWdCLENBQUNDO0lBQzVCLE9BQU9wQix3REFBUSxDQUNiO1FBQ0VjLFFBQVFNLEtBQUtFLEdBQUc7UUFDaEJQLE9BQU9LLEtBQUtMLEtBQUs7UUFDakJDLE1BQU1JLEtBQUtKLElBQUk7SUFDakIsR0FDQUwsUUFBUUMsR0FBRyxDQUFDQyxVQUFVLElBQUksbUJBQzFCO1FBQUVVLFdBQVc7SUFBSztBQUV0QixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL3V0aWxzL2F1dGhVdGlscy5qcz9kMDQ0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcblxuLy8gR2V0IHVzZXIgZGF0YSBmcm9tIEpXVCB0b2tlbiBpbiByZXF1ZXN0IGhlYWRlcnNcbmV4cG9ydCBjb25zdCBnZXRVc2VyRnJvbVRva2VuID0gKHJlcXVlc3QpID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBhdXRoSGVhZGVyID0gcmVxdWVzdC5oZWFkZXJzLmdldCgnYXV0aG9yaXphdGlvbicpO1xuICAgIGlmICghYXV0aEhlYWRlciB8fCAhYXV0aEhlYWRlci5zdGFydHNXaXRoKCdCZWFyZXIgJykpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCB0b2tlbiA9IGF1dGhIZWFkZXIuc3BsaXQoJyAnKVsxXTtcbiAgICBjb25zdCBkZWNvZGVkID0gand0LnZlcmlmeSh0b2tlbiwgcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAneW91ci1zZWNyZXQta2V5Jyk7XG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIHVzZXJJZDogZGVjb2RlZC51c2VySWQsXG4gICAgICBlbWFpbDogZGVjb2RlZC5lbWFpbCxcbiAgICAgIHJvbGU6IGRlY29kZWQucm9sZVxuICAgIH07XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdmVyaWZ5aW5nIHRva2VuOicsIGVycm9yKTtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxufTtcblxuLy8gR2VuZXJhdGUgSldUIHRva2VuXG5leHBvcnQgY29uc3QgZ2VuZXJhdGVUb2tlbiA9ICh1c2VyKSA9PiB7XG4gIHJldHVybiBqd3Quc2lnbihcbiAgICB7XG4gICAgICB1c2VySWQ6IHVzZXIuX2lkLFxuICAgICAgZW1haWw6IHVzZXIuZW1haWwsXG4gICAgICByb2xlOiB1c2VyLnJvbGVcbiAgICB9LFxuICAgIHByb2Nlc3MuZW52LkpXVF9TRUNSRVQgfHwgJ3lvdXItc2VjcmV0LWtleScsXG4gICAgeyBleHBpcmVzSW46ICc3ZCcgfVxuICApO1xufTtcbiJdLCJuYW1lcyI6WyJqd3QiLCJnZXRVc2VyRnJvbVRva2VuIiwicmVxdWVzdCIsImF1dGhIZWFkZXIiLCJoZWFkZXJzIiwiZ2V0Iiwic3RhcnRzV2l0aCIsInRva2VuIiwic3BsaXQiLCJkZWNvZGVkIiwidmVyaWZ5IiwicHJvY2VzcyIsImVudiIsIkpXVF9TRUNSRVQiLCJ1c2VySWQiLCJlbWFpbCIsInJvbGUiLCJlcnJvciIsImNvbnNvbGUiLCJnZW5lcmF0ZVRva2VuIiwidXNlciIsInNpZ24iLCJfaWQiLCJleHBpcmVzSW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./utils/authUtils.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/yallist","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/safe-buffer","vendor-chunks/lru-cache","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Fanalytics%2Froute&page=%2Fapi%2Fblog%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();