/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.jsx */ \"(rsc)/./app/page.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\page.jsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.jsx */ \"(rsc)/./app/layout.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS5qc3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q0RTWVMlNUNEZXNrdG9wJTVDTXIuQmxvZyU1Q2Jsb2ctYXBwJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNEU1lTJTVDRGVza3RvcCU1Q01yLkJsb2clNUNibG9nLWFwcCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsd0lBQStGO0FBQ3RIO0FBQ0Esb0NBQW9DLDhlQUF5UDtBQUM3UjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBLHlCQUF5Qiw0SUFBaUc7QUFDMUgsb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBLG9DQUFvQyw4ZUFBeVA7QUFDN1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvPzI0MzAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcYXBwXFxcXHBhZ2UuanN4XCIpLCBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXGFwcFxcXFxwYWdlLmpzeFwiXSxcbiAgICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcYXBwXFxcXGxheW91dC5qc3hcIiksIFwiQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcYXBwXFxcXGxheW91dC5qc3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcYXBwXFxcXHBhZ2UuanN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./Components/CookieConsent.jsx */ \"(ssr)/./Components/CookieConsent.jsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-toastify/dist/react-toastify.esm.mjs */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNDb21wb25lbnRzJTVDQ29va2llQ29uc2VudC5qc3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNEU1lTJTVDRGVza3RvcCU1Q01yLkJsb2clNUNibG9nLWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlNUMlNUNsYXlvdXQuanN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIyT3V0Zml0JTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTJDJTIyd2VpZ2h0JTIyJTNBJTVCJTIyNDAwJTIyJTJDJTIyNTAwJTIyJTJDJTIyNjAwJTIyJTJDJTIyNzAwJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyb3V0Zml0JTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RTWVMlNUNEZXNrdG9wJTVDTXIuQmxvZyU1Q2Jsb2ctYXBwJTVDbm9kZV9tb2R1bGVzJTVDcmVhY3QtdG9hc3RpZnklNUNkaXN0JTVDcmVhY3QtdG9hc3RpZnkuZXNtLm1qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RTWVMlNUNEZXNrdG9wJTVDTXIuQmxvZyU1Q2Jsb2ctYXBwJTVDbm9kZV9tb2R1bGVzJTVDcmVhY3QtdG9hc3RpZnklNUNkaXN0JTVDUmVhY3RUb2FzdGlmeS5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUErRztBQUMvRyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvP2VhYzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEU1lTXFxcXERlc2t0b3BcXFxcTXIuQmxvZ1xcXFxibG9nLWFwcFxcXFxDb21wb25lbnRzXFxcXENvb2tpZUNvbnNlbnQuanN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEU1lTXFxcXERlc2t0b3BcXFxcTXIuQmxvZ1xcXFxibG9nLWFwcFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtdG9hc3RpZnlcXFxcZGlzdFxcXFxyZWFjdC10b2FzdGlmeS5lc20ubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cpage.jsx&server=true!":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cpage.jsx&server=true! ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.jsx */ \"(ssr)/./app/page.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNhcHAlNUNwYWdlLmpzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLz82OWFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcYXBwXFxcXHBhZ2UuanN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cpage.jsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNhcHAtcm91dGVyLmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RTWVMlNUNEZXNrdG9wJTVDTXIuQmxvZyU1Q2Jsb2ctYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RTWVMlNUNEZXNrdG9wJTVDTXIuQmxvZyU1Q2Jsb2ctYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTZJO0FBQzdJLDBPQUFpSjtBQUNqSix3T0FBZ0o7QUFDaEosa1BBQXFKO0FBQ3JKLHNRQUErSjtBQUMvSiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvPzRlMjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEU1lTXFxcXERlc2t0b3BcXFxcTXIuQmxvZ1xcXFxibG9nLWFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcRFNZU1xcXFxEZXNrdG9wXFxcXE1yLkJsb2dcXFxcYmxvZy1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./Assets/assets.js":
/*!**************************!*\
  !*** ./Assets/assets.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assets: () => (/* binding */ assets),\n/* harmony export */   blog_data: () => (/* binding */ blog_data)\n/* harmony export */ });\n/* harmony import */ var _blog_pic_1_png__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blog_pic_1.png */ \"(ssr)/./Assets/blog_pic_1.png\");\n/* harmony import */ var _blog_pic_2_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./blog_pic_2.png */ \"(ssr)/./Assets/blog_pic_2.png\");\n/* harmony import */ var _blog_pic_3_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./blog_pic_3.png */ \"(ssr)/./Assets/blog_pic_3.png\");\n/* harmony import */ var _blog_pic_4_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog_pic_4.png */ \"(ssr)/./Assets/blog_pic_4.png\");\n/* harmony import */ var _blog_pic_5_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./blog_pic_5.png */ \"(ssr)/./Assets/blog_pic_5.png\");\n/* harmony import */ var _blog_pic_6_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./blog_pic_6.png */ \"(ssr)/./Assets/blog_pic_6.png\");\n/* harmony import */ var _blog_pic_7_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./blog_pic_7.png */ \"(ssr)/./Assets/blog_pic_7.png\");\n/* harmony import */ var _blog_pic_8_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./blog_pic_8.png */ \"(ssr)/./Assets/blog_pic_8.png\");\n/* harmony import */ var _blog_pic_9_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./blog_pic_9.png */ \"(ssr)/./Assets/blog_pic_9.png\");\n/* harmony import */ var _blog_pic_10_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./blog_pic_10.png */ \"(ssr)/./Assets/blog_pic_10.png\");\n/* harmony import */ var _blog_pic_11_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./blog_pic_11.png */ \"(ssr)/./Assets/blog_pic_11.png\");\n/* harmony import */ var _blog_pic_12_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./blog_pic_12.png */ \"(ssr)/./Assets/blog_pic_12.png\");\n/* harmony import */ var _blog_pic_13_png__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./blog_pic_13.png */ \"(ssr)/./Assets/blog_pic_13.png\");\n/* harmony import */ var _blog_pic_14_png__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./blog_pic_14.png */ \"(ssr)/./Assets/blog_pic_14.png\");\n/* harmony import */ var _blog_pic_15_png__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./blog_pic_15.png */ \"(ssr)/./Assets/blog_pic_15.png\");\n/* harmony import */ var _blog_pic_16_png__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./blog_pic_16.png */ \"(ssr)/./Assets/blog_pic_16.png\");\n/* harmony import */ var _facebook_icon_png__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./facebook_icon.png */ \"(ssr)/./Assets/facebook_icon.png\");\n/* harmony import */ var _googleplus_icon_png__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./googleplus_icon.png */ \"(ssr)/./Assets/googleplus_icon.png\");\n/* harmony import */ var _twitter_icon_png__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./twitter_icon.png */ \"(ssr)/./Assets/twitter_icon.png\");\n/* harmony import */ var _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./profile_icon.png */ \"(ssr)/./Assets/profile_icon.png\");\n/* harmony import */ var _logo_png__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./logo.png */ \"(ssr)/./Assets/logo.png\");\n/* harmony import */ var _arrow_png__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./arrow.png */ \"(ssr)/./Assets/arrow.png\");\n/* harmony import */ var _logo_light_png__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./logo_light.png */ \"(ssr)/./Assets/logo_light.png\");\n/* harmony import */ var _blog_icon_png__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./blog_icon.png */ \"(ssr)/./Assets/blog_icon.png\");\n/* harmony import */ var _add_icon_png__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./add_icon.png */ \"(ssr)/./Assets/add_icon.png\");\n/* harmony import */ var _email_icon_png__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./email_icon.png */ \"(ssr)/./Assets/email_icon.png\");\n/* harmony import */ var _upload_area_png__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./upload_area.png */ \"(ssr)/./Assets/upload_area.png\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst assets = {\n    facebook_icon: _facebook_icon_png__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    googleplus_icon: _googleplus_icon_png__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    twitter_icon: _twitter_icon_png__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    profile_icon: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    logo: _logo_png__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    arrow: _arrow_png__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n    logo_light: _logo_light_png__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n    blog_icon: _blog_icon_png__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n    add_icon: _add_icon_png__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n    email_icon: _email_icon_png__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n    upload_area: _upload_area_png__WEBPACK_IMPORTED_MODULE_26__[\"default\"]\n};\nconst blog_data = [\n    {\n        id: 1,\n        title: \"A detailed step by step guide to manage your lifestyle\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_1_png__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 2,\n        title: \"How to create an effective startup roadmap or ideas\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_2_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 3,\n        title: \"Learning new technology to boost your career in software\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_3_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 4,\n        title: \"Tips for getting the most out of apps and software\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_4_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 5,\n        title: \"Enhancing your skills and capturing memorable moments\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_5_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 6,\n        title: \"Maximizing returns by minimizing resources in your startup\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_6_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 7,\n        title: \"Technology for Career advancement in development\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_7_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 8,\n        title: \"A comprehensive roadmap for effective lifestyle management\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_8_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 9,\n        title: \"Achieving maximum returns with minimal resources\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_9_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 10,\n        title: \"Beyond the Ordinary: Crafting Your Exceptional Lifestyle\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_10_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 11,\n        title: \"Unveiling the Secrets of Successful Startups in Technolgy\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_11_png__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 12,\n        title: \"How to design an online Learning Platform today\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_12_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 13,\n        title: \"Tomorrow's Algorithms: Shaping the Landscape of Future AI\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_13_png__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 14,\n        title: \"Balance & Bliss: Navigating Life's Journey with Style\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_14_png__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 15,\n        title: \"Exploring the Evolution of social networking in the Future\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_15_png__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 16,\n        title: \"Shaping the Future of statup ecosystem in the world\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_16_png__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYXNzZXRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0c7QUFDSTtBQUNOO0FBQ0E7QUFDaEI7QUFDRTtBQUNVO0FBQ0Y7QUFDRjtBQUNJO0FBQ0U7QUFFcEMsTUFBTTJCLFNBQVM7SUFDbEJYLGFBQWFBLDZEQUFBQTtJQUNiQyxlQUFlQSwrREFBQUE7SUFDZkMsWUFBWUEsNERBQUFBO0lBQ1pDLFlBQVlBLDREQUFBQTtJQUNaQyxJQUFJQSxvREFBQUE7SUFDSkMsS0FBS0EscURBQUFBO0lBQ0xDLFVBQVVBLDBEQUFBQTtJQUNWQyxTQUFTQSx5REFBQUE7SUFDVEMsUUFBUUEsd0RBQUFBO0lBQ1JDLFVBQVVBLDBEQUFBQTtJQUNWQyxXQUFXQSwyREFBQUE7QUFDWCxFQUFDO0FBRU0sTUFBTUUsWUFBWTtJQUFDO1FBQ3RCQyxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNaEMsdURBQVVBO1FBQ2hCaUMsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU0vQix1REFBVUE7UUFDaEJnQyxNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTTlCLHVEQUFVQTtRQUNoQitCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNN0IsdURBQVVBO1FBQ2hCOEIsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU01Qix1REFBVUE7UUFDaEI2QixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTTNCLHVEQUFVQTtRQUNoQjRCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNMUIsdURBQVVBO1FBQ2hCMkIsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU16Qix1REFBVUE7UUFDaEIwQixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTXhCLHVEQUFVQTtRQUNoQnlCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNdkIsd0RBQVdBO1FBQ2pCd0IsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU10Qix5REFBV0E7UUFDakJ1QixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTXJCLHlEQUFXQTtRQUNqQnNCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNcEIseURBQVdBO1FBQ2pCcUIsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU1uQix5REFBV0E7UUFDakJvQixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTWxCLHlEQUFXQTtRQUNqQm1CLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNakIseURBQVdBO1FBQ2pCa0IsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtDQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9hc3NldHMuanM/YzUxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYmxvZ19waWNfMSBmcm9tICcuL2Jsb2dfcGljXzEucG5nJztcclxuaW1wb3J0IGJsb2dfcGljXzIgZnJvbSAnLi9ibG9nX3BpY18yLnBuZyc7XHJcbmltcG9ydCBibG9nX3BpY18zIGZyb20gJy4vYmxvZ19waWNfMy5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfNCBmcm9tICcuL2Jsb2dfcGljXzQucG5nJztcclxuaW1wb3J0IGJsb2dfcGljXzUgZnJvbSAnLi9ibG9nX3BpY181LnBuZyc7XHJcbmltcG9ydCBibG9nX3BpY182IGZyb20gJy4vYmxvZ19waWNfNi5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfNyBmcm9tICcuL2Jsb2dfcGljXzcucG5nJztcclxuaW1wb3J0IGJsb2dfcGljXzggZnJvbSAnLi9ibG9nX3BpY184LnBuZyc7XHJcbmltcG9ydCBibG9nX3BpY185IGZyb20gJy4vYmxvZ19waWNfOS5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTAgZnJvbSAnLi9ibG9nX3BpY18xMC5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTEgZnJvbSAnLi9ibG9nX3BpY18xMS5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTIgZnJvbSAnLi9ibG9nX3BpY18xMi5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTMgZnJvbSAnLi9ibG9nX3BpY18xMy5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTQgZnJvbSAnLi9ibG9nX3BpY18xNC5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTUgZnJvbSAnLi9ibG9nX3BpY18xNS5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTYgZnJvbSAnLi9ibG9nX3BpY18xNi5wbmcnO1xyXG5pbXBvcnQgZmFjZWJvb2tfaWNvbiBmcm9tICcuL2ZhY2Vib29rX2ljb24ucG5nJ1xyXG5pbXBvcnQgZ29vZ2xlcGx1c19pY29uIGZyb20gJy4vZ29vZ2xlcGx1c19pY29uLnBuZydcclxuaW1wb3J0IHR3aXR0ZXJfaWNvbiBmcm9tICcuL3R3aXR0ZXJfaWNvbi5wbmcnXHJcbmltcG9ydCBwcm9maWxlX2ljb24gZnJvbSAnLi9wcm9maWxlX2ljb24ucG5nJ1xyXG5pbXBvcnQgbG9nbyBmcm9tICcuL2xvZ28ucG5nJ1xyXG5pbXBvcnQgYXJyb3cgZnJvbSAnLi9hcnJvdy5wbmcnXHJcbmltcG9ydCBsb2dvX2xpZ2h0IGZyb20gJy4vbG9nb19saWdodC5wbmcnXHJcbmltcG9ydCBibG9nX2ljb24gZnJvbSAnLi9ibG9nX2ljb24ucG5nJ1xyXG5pbXBvcnQgYWRkX2ljb24gZnJvbSAnLi9hZGRfaWNvbi5wbmcnXHJcbmltcG9ydCBlbWFpbF9pY29uIGZyb20gJy4vZW1haWxfaWNvbi5wbmcnXHJcbmltcG9ydCB1cGxvYWRfYXJlYSBmcm9tICcuL3VwbG9hZF9hcmVhLnBuZydcclxuXHJcbmV4cG9ydCBjb25zdCBhc3NldHMgPSB7XHJcbiAgICBmYWNlYm9va19pY29uLFxyXG4gICAgZ29vZ2xlcGx1c19pY29uLFxyXG4gICAgdHdpdHRlcl9pY29uLFxyXG4gICAgcHJvZmlsZV9pY29uLFxyXG4gICAgbG9nbyxcclxuICAgIGFycm93LFxyXG4gICAgbG9nb19saWdodCxcclxuICAgIGJsb2dfaWNvbixcclxuICAgIGFkZF9pY29uLFxyXG4gICAgZW1haWxfaWNvbixcclxuICAgIHVwbG9hZF9hcmVhXHJcbiAgICB9XHJcblxyXG4gICAgZXhwb3J0IGNvbnN0IGJsb2dfZGF0YSA9IFt7XHJcbiAgICAgICAgaWQ6MSxcclxuICAgICAgICB0aXRsZTpcIkEgZGV0YWlsZWQgc3RlcCBieSBzdGVwIGd1aWRlIHRvIG1hbmFnZSB5b3VyIGxpZmVzdHlsZVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY18xLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIkxpZmVzdHlsZVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjIsXHJcbiAgICAgICAgdGl0bGU6XCJIb3cgdG8gY3JlYXRlIGFuIGVmZmVjdGl2ZSBzdGFydHVwIHJvYWRtYXAgb3IgaWRlYXNcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjpcIkxvcmVtIElwc3VtIGlzIHNpbXBseSBkdW1teSB0ZXh0IG9mIHRoZSBwcmludGluZyBhbmQgdHlwZXNldHRpbmcgaW5kdXN0cnkuIExvcmVtIElwc3VtIGhhcyBiZWVuIHRoZS4uXCIsXHJcbiAgICAgICAgaW1hZ2U6YmxvZ19waWNfMixcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJTdGFydHVwXCIsXHJcbiAgICAgICAgYXV0aG9yOlwiQWxleCBCZW5uZXR0XCIsXHJcbiAgICAgICAgYXV0aG9yX2ltZzpwcm9maWxlX2ljb25cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgaWQ6MyxcclxuICAgICAgICB0aXRsZTpcIkxlYXJuaW5nIG5ldyB0ZWNobm9sb2d5IHRvIGJvb3N0IHlvdXIgY2FyZWVyIGluIHNvZnR3YXJlXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzMsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiVGVjaG5vbG9neVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjQsXHJcbiAgICAgICAgdGl0bGU6XCJUaXBzIGZvciBnZXR0aW5nIHRoZSBtb3N0IG91dCBvZiBhcHBzIGFuZCBzb2Z0d2FyZVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY180LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlRlY2hub2xvZ3lcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDo1LFxyXG4gICAgICAgIHRpdGxlOlwiRW5oYW5jaW5nIHlvdXIgc2tpbGxzIGFuZCBjYXB0dXJpbmcgbWVtb3JhYmxlIG1vbWVudHNcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjpcIkxvcmVtIElwc3VtIGlzIHNpbXBseSBkdW1teSB0ZXh0IG9mIHRoZSBwcmludGluZyBhbmQgdHlwZXNldHRpbmcgaW5kdXN0cnkuIExvcmVtIElwc3VtIGhhcyBiZWVuIHRoZS4uXCIsXHJcbiAgICAgICAgaW1hZ2U6YmxvZ19waWNfNSxcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJMaWZlc3R5bGVcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDo2LFxyXG4gICAgICAgIHRpdGxlOlwiTWF4aW1pemluZyByZXR1cm5zIGJ5IG1pbmltaXppbmcgcmVzb3VyY2VzIGluIHlvdXIgc3RhcnR1cFwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY182LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDo3LFxyXG4gICAgICAgIHRpdGxlOlwiVGVjaG5vbG9neSBmb3IgQ2FyZWVyIGFkdmFuY2VtZW50IGluIGRldmVsb3BtZW50XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzcsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiVGVjaG5vbG9neVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjgsXHJcbiAgICAgICAgdGl0bGU6XCJBIGNvbXByZWhlbnNpdmUgcm9hZG1hcCBmb3IgZWZmZWN0aXZlIGxpZmVzdHlsZSBtYW5hZ2VtZW50XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzgsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiTGlmZXN0eWxlXCIsXHJcbiAgICAgICAgYXV0aG9yOlwiQWxleCBCZW5uZXR0XCIsXHJcbiAgICAgICAgYXV0aG9yX2ltZzpwcm9maWxlX2ljb25cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgaWQ6OSxcclxuICAgICAgICB0aXRsZTpcIkFjaGlldmluZyBtYXhpbXVtIHJldHVybnMgd2l0aCBtaW5pbWFsIHJlc291cmNlc1wiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY185LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxMCxcclxuICAgICAgICB0aXRsZTpcIkJleW9uZCB0aGUgT3JkaW5hcnk6IENyYWZ0aW5nIFlvdXIgRXhjZXB0aW9uYWwgTGlmZXN0eWxlXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzEwLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIkxpZmVzdHlsZVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjExLFxyXG4gICAgICAgIHRpdGxlOlwiVW52ZWlsaW5nIHRoZSBTZWNyZXRzIG9mIFN1Y2Nlc3NmdWwgU3RhcnR1cHMgaW4gVGVjaG5vbGd5XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzExLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxMixcclxuICAgICAgICB0aXRsZTpcIkhvdyB0byBkZXNpZ24gYW4gb25saW5lIExlYXJuaW5nIFBsYXRmb3JtIHRvZGF5XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzEyLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlRlY2hub2xvZ3lcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxMyxcclxuICAgICAgICB0aXRsZTpcIlRvbW9ycm93J3MgQWxnb3JpdGhtczogU2hhcGluZyB0aGUgTGFuZHNjYXBlIG9mIEZ1dHVyZSBBSVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY18xMyxcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJTdGFydHVwXCIsXHJcbiAgICAgICAgYXV0aG9yOlwiQWxleCBCZW5uZXR0XCIsXHJcbiAgICAgICAgYXV0aG9yX2ltZzpwcm9maWxlX2ljb25cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgaWQ6MTQsXHJcbiAgICAgICAgdGl0bGU6XCJCYWxhbmNlICYgQmxpc3M6IE5hdmlnYXRpbmcgTGlmZSdzIEpvdXJuZXkgd2l0aCBTdHlsZVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY18xNCxcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJMaWZlc3R5bGVcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxNSxcclxuICAgICAgICB0aXRsZTpcIkV4cGxvcmluZyB0aGUgRXZvbHV0aW9uIG9mIHNvY2lhbCBuZXR3b3JraW5nIGluIHRoZSBGdXR1cmVcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjpcIkxvcmVtIElwc3VtIGlzIHNpbXBseSBkdW1teSB0ZXh0IG9mIHRoZSBwcmludGluZyBhbmQgdHlwZXNldHRpbmcgaW5kdXN0cnkuIExvcmVtIElwc3VtIGhhcyBiZWVuIHRoZS4uXCIsXHJcbiAgICAgICAgaW1hZ2U6YmxvZ19waWNfMTUsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiVGVjaG5vbG9neVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjE2LFxyXG4gICAgICAgIHRpdGxlOlwiU2hhcGluZyB0aGUgRnV0dXJlIG9mIHN0YXR1cCBlY29zeXN0ZW0gaW4gdGhlIHdvcmxkXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzE2LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuXSJdLCJuYW1lcyI6WyJibG9nX3BpY18xIiwiYmxvZ19waWNfMiIsImJsb2dfcGljXzMiLCJibG9nX3BpY180IiwiYmxvZ19waWNfNSIsImJsb2dfcGljXzYiLCJibG9nX3BpY183IiwiYmxvZ19waWNfOCIsImJsb2dfcGljXzkiLCJibG9nX3BpY18xMCIsImJsb2dfcGljXzExIiwiYmxvZ19waWNfMTIiLCJibG9nX3BpY18xMyIsImJsb2dfcGljXzE0IiwiYmxvZ19waWNfMTUiLCJibG9nX3BpY18xNiIsImZhY2Vib29rX2ljb24iLCJnb29nbGVwbHVzX2ljb24iLCJ0d2l0dGVyX2ljb24iLCJwcm9maWxlX2ljb24iLCJsb2dvIiwiYXJyb3ciLCJsb2dvX2xpZ2h0IiwiYmxvZ19pY29uIiwiYWRkX2ljb24iLCJlbWFpbF9pY29uIiwidXBsb2FkX2FyZWEiLCJhc3NldHMiLCJibG9nX2RhdGEiLCJpZCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJpbWFnZSIsImRhdGUiLCJEYXRlIiwibm93IiwiY2F0ZWdvcnkiLCJhdXRob3IiLCJhdXRob3JfaW1nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/assets.js\n");

/***/ }),

/***/ "(ssr)/./Components/BlogItem.jsx":
/*!*********************************!*\
  !*** ./Components/BlogItem.jsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(ssr)/./Assets/assets.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _utils_textUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/textUtils */ \"(ssr)/./utils/textUtils.js\");\n\n\n\n\n\n\nconst BlogItem = ({ title, description, category, image, id })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-[330px] sm:max-w-[300px] bg-white border border-black transition-all hover:shadow-[-7px_7px_0px_#000000]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                href: `/blogs/${id}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: image,\n                    alt: \"\",\n                    width: 400,\n                    height: 400,\n                    className: \"border-b border-black\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogItem.jsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogItem.jsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"ml-5 mt-5 px-1 inline-block bg-black text-white text-sm\",\n                children: category\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogItem.jsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"mb-2 text-lg font-medium tracking-tight text-gray-900\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogItem.jsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-3 text-sm tracking-tight text-gray-700\",\n                        dangerouslySetInnerHTML: {\n                            \"__html\": (0,_utils_textUtils__WEBPACK_IMPORTED_MODULE_5__.cleanDescriptionForPreview)(description).slice(0, 120)\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogItem.jsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: `/blogs/${id}`,\n                        className: \"inline-flex items-center py-2 font-semibold text-center\",\n                        children: [\n                            \"Read more \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.arrow,\n                                className: \"ml-2\",\n                                alt: \"\",\n                                width: 12\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogItem.jsx\",\n                                lineNumber: 19,\n                                columnNumber: 23\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogItem.jsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogItem.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogItem.jsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BlogItem);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/BlogItem.jsx\n");

/***/ }),

/***/ "(ssr)/./Components/BlogList.jsx":
/*!*********************************!*\
  !*** ./Components/BlogList.jsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _BlogItem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BlogItem */ \"(ssr)/./Components/BlogItem.jsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n\n\n\nconst BLOGS_PER_PAGE = 12;\nconst BlogList = ({ searchTerm = \"\" })=>{\n    const [menu, setMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [blogs, setBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showCategoryDropdown, setShowCategoryDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fetch data\n    const fetchBlogs = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/blog\");\n            setBlogs(response.data.blogs);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n            setLoading(false);\n        }\n    };\n    const fetchCategories = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/categories\");\n            if (response.data.success && response.data.categories.length > 0) {\n                setCategories(response.data.categories);\n            } else {\n                // Fallback to default categories if none found\n                setCategories([\n                    {\n                        _id: \"1\",\n                        name: \"Startup\"\n                    },\n                    {\n                        _id: \"2\",\n                        name: \"Technology\"\n                    },\n                    {\n                        _id: \"3\",\n                        name: \"Lifestyle\"\n                    }\n                ]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n            // Fallback to default categories on error\n            setCategories([\n                {\n                    _id: \"1\",\n                    name: \"Startup\"\n                },\n                {\n                    _id: \"2\",\n                    name: \"Technology\"\n                },\n                {\n                    _id: \"3\",\n                    name: \"Lifestyle\"\n                }\n            ]);\n        }\n    };\n    // Set mounted state to prevent hydration mismatch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMounted(true);\n        fetchBlogs();\n        fetchCategories();\n    }, []);\n    // Reset to first page when menu changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setCurrentPage(1);\n    }, [\n        menu\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isMounted) return;\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                setShowCategoryDropdown(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isMounted\n    ]);\n    // Filter blogs by category and search term\n    const filteredBlogs = blogs.filter((item)=>{\n        const matchesCategory = menu === \"All\" ? true : item.category === menu;\n        const matchesSearch = searchTerm.trim() === \"\" ? true : item.title.toLowerCase().includes(searchTerm.toLowerCase()) || item.description.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesCategory && matchesSearch;\n    });\n    const totalPages = Math.ceil(filteredBlogs.length / BLOGS_PER_PAGE);\n    const startIdx = (currentPage - 1) * BLOGS_PER_PAGE;\n    const currentBlogs = filteredBlogs.slice(startIdx, startIdx + BLOGS_PER_PAGE);\n    const handleCategorySelect = (categoryName)=>{\n        setMenu(categoryName);\n        setShowCategoryDropdown(false);\n    };\n    // Return a loading state until client-side hydration is complete\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-10\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n            lineNumber: 101,\n            columnNumber: 16\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center my-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setMenu(\"All\"),\n                            className: `py-2 px-6 rounded-md transition-colors ${menu === \"All\" ? \"bg-black text-white\" : \"bg-gray-100 hover:bg-gray-200\"}`,\n                            children: \"All\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                            lineNumber: 108,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            ref: dropdownRef,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCategoryDropdown(!showCategoryDropdown),\n                                    className: `py-2 px-6 rounded-md flex items-center gap-2 transition-colors ${menu !== \"All\" ? \"bg-black text-white\" : \"bg-gray-100 hover:bg-gray-200\"}`,\n                                    children: [\n                                        menu !== \"All\" ? menu : \"Categories\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: `h-4 w-4 transition-transform ${showCategoryDropdown ? \"rotate-180\" : \"\"}`,\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 9l-7 7-7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 25\n                                }, undefined),\n                                showCategoryDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute z-10 mt-1 w-48 bg-white rounded-md shadow-lg py-1 max-h-60 overflow-auto\",\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleCategorySelect(category.name),\n                                            className: `block w-full text-left px-4 py-2 text-sm ${menu === category.name ? \"bg-gray-100 font-medium\" : \"hover:bg-gray-50\"}`,\n                                            children: category.name\n                                        }, category._id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 37\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                    lineNumber: 107,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                lineNumber: 106,\n                columnNumber: 13\n            }, undefined),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-10\",\n                children: \"Loading blogs...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                lineNumber: 162,\n                columnNumber: 17\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-32 xl:mx-24\",\n                        children: currentBlogs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BlogItem__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                id: item._id,\n                                image: item.image,\n                                title: item.title,\n                                description: item.description,\n                                category: item.category\n                            }, item._id || index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                lineNumber: 167,\n                                columnNumber: 29\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                        lineNumber: 165,\n                        columnNumber: 21\n                    }, undefined),\n                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-0 mb-8 gap-2\",\n                        children: Array.from({\n                            length: totalPages\n                        }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentPage(i + 1),\n                                className: `px-4 py-2 border border-black rounded ${currentPage === i + 1 ? \"bg-black text-white\" : \"bg-white text-black hover:bg-gray-100\"}`,\n                                children: i + 1\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                                lineNumber: 181,\n                                columnNumber: 33\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n                        lineNumber: 179,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\BlogList.jsx\",\n        lineNumber: 105,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BlogList);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/BlogList.jsx\n");

/***/ }),

/***/ "(ssr)/./Components/CookieConsent.jsx":
/*!**************************************!*\
  !*** ./Components/CookieConsent.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/cookieUtils */ \"(ssr)/./utils/cookieUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CookieConsent = ()=>{\n    const [showConsent, setShowConsent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user has already made a choice\n        const consentCookie = (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"cookie-consent\");\n        if (!consentCookie) {\n            // Set a timeout to show the consent popup after 10 seconds\n            const timer = setTimeout(()=>{\n                setShowConsent(true);\n            }, 10000) // 10 seconds\n            ;\n            return ()=>clearTimeout(timer);\n        } else if (consentCookie === \"accepted\") {\n            // If user has accepted cookies, set the necessary cookies\n            (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setAnalyticsCookies)();\n            (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setFunctionalCookies)();\n        }\n    }, []);\n    const acceptCookies = ()=>{\n        // Set cookie with 1-year expiry\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setCookie)(\"cookie-consent\", \"accepted\", {\n            expires: 365\n        });\n        // Set other cookies as needed\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setAnalyticsCookies)();\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setFunctionalCookies)();\n        setShowConsent(false);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Cookie preferences saved\");\n    };\n    const declineCookies = ()=>{\n        // Set cookie to remember user declined\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setCookie)(\"cookie-consent\", \"declined\", {\n            expires: 365\n        });\n        setShowConsent(false);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Cookies declined. Some features may be limited.\");\n    };\n    if (!showConsent) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-0 left-0 right-0 bg-white shadow-lg z-50 border-t border-gray-200 p-4 md:p-6 animate-fade-in\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row md:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"We use cookies\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm md:text-base\",\n                                children: [\n                                    'We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking \"Accept All\", you consent to our use of cookies.',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/privacy-policy\",\n                                        className: \"text-blue-600 hover:underline ml-1\",\n                                        children: \"Read our Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: declineCookies,\n                                className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 text-sm md:text-base\",\n                                children: \"Decline\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: acceptCookies,\n                                className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 text-sm md:text-base\",\n                                children: \"Accept All\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieConsent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/CookieConsent.jsx\n");

/***/ }),

/***/ "(ssr)/./Components/Footer.jsx":
/*!*******************************!*\
  !*** ./Components/Footer.jsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(ssr)/./Assets/assets.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst Footer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-around flex-col gap-2 sm:gap-0 sm:flex-row bg-black py-5 items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.logo_light,\n                alt: \"Mr.Blogger\",\n                width: 120\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center gap-4 mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/about\",\n                                className: \"text-sm text-white hover:underline\",\n                                children: \"About Us\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                                lineNumber: 12,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/contact\",\n                                className: \"text-sm text-white hover:underline\",\n                                children: \"Contact Us\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-white\",\n                        children: \"All right reserved. Copyright @Mr.Blogger\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.facebook_icon,\n                        alt: \"\",\n                        width: 40\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.twitter_icon,\n                        alt: \"\",\n                        width: 40\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.googleplus_icon,\n                        alt: \"\",\n                        width: 40\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/Footer.jsx\n");

/***/ }),

/***/ "(ssr)/./Components/Header.jsx":
/*!*******************************!*\
  !*** ./Components/Header.jsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(ssr)/./Assets/assets.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Header = ({ setSearchTerm })=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLoginModal, setShowLoginModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isRegistering, setIsRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showAccountDropdown, setShowAccountDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [loginData, setLoginData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [registerData, setRegisterData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        role: \"user\"\n    });\n    const [userProfilePicture, setUserProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"/default_profile.png\");\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLogoutConfirm, setShowLogoutConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isSearchMode, setIsSearchMode] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [localSearchTerm, setLocalSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // Add this function to toggle password visibility\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    // Check if user is logged in on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const authToken = localStorage.getItem(\"authToken\");\n        const storedUserRole = localStorage.getItem(\"userRole\");\n        const storedUserId = localStorage.getItem(\"userId\");\n        const storedProfilePicture = localStorage.getItem(\"userProfilePicture\");\n        const storedUserName = localStorage.getItem(\"userName\");\n        if (authToken) {\n            setIsLoggedIn(true);\n            setUserRole(storedUserRole || \"user\");\n            if (storedProfilePicture) {\n                setUserProfilePicture(storedProfilePicture);\n            }\n            if (storedUserName) {\n                setUserName(storedUserName);\n            }\n        }\n    }, []);\n    const onSubmitHandler = async (e)=>{\n        // Existing email subscription code\n        e.preventDefault();\n        const formData = new FormData();\n        formData.append(\"email\", email);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/email\", formData);\n        if (response.data.success) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(response.data.msg);\n            setEmail(\"\");\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Error\");\n        }\n    };\n    const handleLoginChange = (e)=>{\n        setLoginData({\n            ...loginData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleRegisterChange = (e)=>{\n        setRegisterData({\n            ...registerData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleLoginSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/auth\", {\n                email: loginData.email,\n                password: loginData.password\n            });\n            if (response.data.success) {\n                // Store auth data in localStorage\n                localStorage.setItem(\"authToken\", response.data.token || \"dummy-token\");\n                localStorage.setItem(\"userRole\", response.data.user.role);\n                localStorage.setItem(\"userId\", response.data.user.id);\n                localStorage.setItem(\"userProfilePicture\", response.data.user.profilePicture);\n                localStorage.setItem(\"userName\", response.data.user.name || \"\");\n                // Update state\n                setIsLoggedIn(true);\n                setUserRole(response.data.user.role);\n                setUserProfilePicture(response.data.user.profilePicture);\n                setUserName(response.data.user.name || \"\");\n                setShowLoginModal(false);\n                // Check if user is admin\n                if (response.data.user.role === \"admin\") {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    window.location.href = \"/admin\";\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    // Redirect regular users to homepage or user dashboard\n                    window.location.href = \"/\";\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Invalid credentials\");\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.response?.data?.message || \"Login failed\");\n        }\n    };\n    const handleRegisterSubmit = async (e)=>{\n        e.preventDefault();\n        // Validate passwords match\n        if (registerData.password !== registerData.confirmPassword) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Passwords do not match\");\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/register\", {\n                email: registerData.email,\n                password: registerData.password,\n                role: registerData.role // Always \"user\" for public registration\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Registration successful! Please login.\");\n                // Switch back to login form\n                setIsRegistering(false);\n                // Pre-fill email in login form\n                setLoginData({\n                    ...loginData,\n                    email: registerData.email\n                });\n                // Reset register form\n                setRegisterData({\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\",\n                    role: \"user\"\n                });\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(response.data.message || \"Registration failed\");\n            }\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.response?.data?.message || \"Registration failed\");\n        }\n    };\n    const toggleForm = ()=>{\n        setIsRegistering(!isRegistering);\n    };\n    const handleLogoutClick = ()=>{\n        setShowLogoutConfirm(true);\n        setShowAccountDropdown(false);\n    };\n    const handleLogoutConfirm = ()=>{\n        // Clear auth data from localStorage\n        localStorage.removeItem(\"authToken\");\n        localStorage.removeItem(\"userRole\");\n        localStorage.removeItem(\"userId\");\n        localStorage.removeItem(\"userProfilePicture\");\n        localStorage.removeItem(\"userName\");\n        // Update state\n        setIsLoggedIn(false);\n        setUserRole(\"\");\n        setShowLogoutConfirm(false);\n        // Show toast and wait briefly before redirecting\n        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Logged out successfully\");\n        // Add a small delay before any navigation\n        setTimeout(()=>{\n            window.location.href = \"/\";\n        }, 300);\n    };\n    const handleLogoutCancel = ()=>{\n        setShowLogoutConfirm(false);\n    };\n    const toggleAccountDropdown = ()=>{\n        setShowAccountDropdown(!showAccountDropdown);\n    };\n    // Search handler (for now, just alert or log, you can wire to blog list)\n    const onSearchHandler = (e)=>{\n        e.preventDefault();\n        if (!localSearchTerm.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Please enter a search term\");\n            return;\n        }\n        setSearchTerm(localSearchTerm);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-5 px-5 md:px-12 lg:px-28\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.logo,\n                        width: 180,\n                        alt: \"\",\n                        className: \"w-[130px] sm:w-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/profile\"),\n                                className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: userProfilePicture,\n                                        width: 24,\n                                        height: 24,\n                                        alt: \"Account\",\n                                        className: \"w-6 h-6 rounded-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: userName || \"Account\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowLoginModal(true),\n                            className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]\",\n                            children: [\n                                \"Get started \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.arrow,\n                                    alt: \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 27\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            showLoginModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-8 rounded-md shadow-lg w-96\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: isRegistering ? \"Register\" : \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, undefined),\n                        isRegistering ? // Registration Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleRegisterSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: registerData.email,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            name: \"password\",\n                                            value: registerData.password,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            name: \"confirmPassword\",\n                                            value: registerData.confirmPassword,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Already have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 254,\n                            columnNumber: 15\n                        }, undefined) : // Login Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLoginSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: loginData.email,\n                                            onChange: handleLoginChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    name: \"password\",\n                                                    value: loginData.password,\n                                                    onChange: handleLoginChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md pr-10\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: togglePasswordVisibility,\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600\",\n                                                    children: showPassword ? // Eye-slash icon (hidden)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 25\n                                                    }, undefined) : // Eye icon (visible)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Register\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 318,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                    lineNumber: 249,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, undefined),\n            showLogoutConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-md shadow-lg max-w-sm w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Confirm Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 399,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-6\",\n                            children: \"Are you sure you want to log out? You will need to log in again to access your account.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutCancel,\n                                    className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutConfirm,\n                                    className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 401,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                    lineNumber: 398,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 397,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center my-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl sm:text-5xl font-medium\",\n                        children: \"Mr.Blogger\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-10 max-w-[740px] m-auto text-xs sm:text-base\",\n                        children: \"Discover insightful articles, trending tech news, startup journeys, and lifestyle stories — all in one place. Welcome to your daily dose of inspiration and knowledge.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: isSearchMode ? onSearchHandler : onSubmitHandler,\n                        className: \"flex justify-between max-w-[500px] scale-75 sm:scale-100 mx-auto mt-10 border border-black shadow-[-7px_7px_0px_#000000]\",\n                        action: \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                onChange: isSearchMode ? (e)=>{\n                                    setLocalSearchTerm(e.target.value);\n                                    if (e.target.value === \"\") setSearchTerm(\"\");\n                                } : (e)=>setEmail(e.target.value),\n                                value: isSearchMode ? localSearchTerm : email,\n                                type: isSearchMode ? \"text\" : \"email\",\n                                placeholder: isSearchMode ? \"Search blogs...\" : \"Enter your email\",\n                                className: \"pl-4 outline-none flex-1\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"border-l border-black py-4 px-4 sm:px-8 active:bg-gray-600 active:text-white\",\n                                children: isSearchMode ? \"Search\" : \"Subscribe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setIsSearchMode((prev)=>{\n                                                if (prev) {\n                                                    setLocalSearchTerm(\"\");\n                                                    setSearchTerm(\"\"); // Clear parent search when toggling back\n                                                }\n                                                return !prev;\n                                            });\n                                        },\n                                        onMouseEnter: ()=>setShowTooltip(true),\n                                        onMouseLeave: ()=>setShowTooltip(false),\n                                        className: \"flex items-center justify-center px-4 border-l border-black hover:bg-gray-100 transition-colors\",\n                                        style: {\n                                            minWidth: \"56px\"\n                                        },\n                                        children: isSearchMode ? // Mail/Envelope Icon\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"22\",\n                                            height: \"22\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                    x: \"3\",\n                                                    y: \"5\",\n                                                    width: \"18\",\n                                                    height: \"14\",\n                                                    rx: \"2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                    points: \"3,7 12,13 21,7\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, undefined) : // Search Icon\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"22\",\n                                            height: \"22\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"11\",\n                                                    cy: \"11\",\n                                                    r: \"8\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    fill: \"none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"21\",\n                                                    y1: \"21\",\n                                                    x2: \"16.65\",\n                                                    y2: \"16.65\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 -translate-x-1/2 bg-black text-white text-xs rounded px-3 py-1 shadow-lg animate-fade-in z-10 whitespace-nowrap\",\n                                        children: isSearchMode ? \"Switch to Subscribe mode\" : \"Switch to Search mode\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Db21wb25lbnRzL0hlYWRlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3lDO0FBQ1Y7QUFDRjtBQUNlO0FBQ087QUFDWjtBQUNiO0FBRTFCLE1BQU1TLFNBQVMsQ0FBQyxFQUFFQyxhQUFhLEVBQUU7SUFDL0IsTUFBTUMsU0FBU1IsMERBQVNBO0lBQ3hCLE1BQU0sQ0FBQ1MsT0FBT0MsU0FBUyxHQUFHUCwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNRLFlBQVlDLGNBQWMsR0FBR1QsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDVSxVQUFVQyxZQUFZLEdBQUdYLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ1ksZ0JBQWdCQyxrQkFBa0IsR0FBR2IsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDYyxlQUFlQyxpQkFBaUIsR0FBR2YsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDZ0IscUJBQXFCQyx1QkFBdUIsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ2tCLFdBQVdDLGFBQWEsR0FBR25CLCtDQUFRQSxDQUFDO1FBQ3pDTSxPQUFPO1FBQ1BjLFVBQVU7SUFDWjtJQUNBLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUd0QiwrQ0FBUUEsQ0FBQztRQUMvQ00sT0FBTztRQUNQYyxVQUFVO1FBQ1ZHLGlCQUFpQjtRQUNqQkMsTUFBTTtJQUNSO0lBQ0EsTUFBTSxDQUFDQyxvQkFBb0JDLHNCQUFzQixHQUFHMUIsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDMkIsVUFBVUMsWUFBWSxHQUFHNUIsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDNkIsbUJBQW1CQyxxQkFBcUIsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQytCLGNBQWNDLGdCQUFnQixHQUFHaEMsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDaUMsY0FBY0MsZ0JBQWdCLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNtQyxpQkFBaUJDLG1CQUFtQixHQUFHcEMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDcUMsYUFBYUMsZUFBZSxHQUFHdEMsK0NBQVFBLENBQUM7SUFFL0Msa0RBQWtEO0lBQ2xELE1BQU11QywyQkFBMkI7UUFDL0JQLGdCQUFnQixDQUFDRDtJQUNuQjtJQUVBLGdEQUFnRDtJQUNoRGhDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXlDLFlBQVlDLGFBQWFDLE9BQU8sQ0FBQztRQUN2QyxNQUFNQyxpQkFBaUJGLGFBQWFDLE9BQU8sQ0FBQztRQUM1QyxNQUFNRSxlQUFlSCxhQUFhQyxPQUFPLENBQUM7UUFDMUMsTUFBTUcsdUJBQXVCSixhQUFhQyxPQUFPLENBQUM7UUFDbEQsTUFBTUksaUJBQWlCTCxhQUFhQyxPQUFPLENBQUM7UUFFNUMsSUFBSUYsV0FBVztZQUNiL0IsY0FBYztZQUNkRSxZQUFZZ0Msa0JBQWtCO1lBQzlCLElBQUlFLHNCQUFzQjtnQkFDeEJuQixzQkFBc0JtQjtZQUN4QjtZQUNBLElBQUlDLGdCQUFnQjtnQkFDbEJsQixZQUFZa0I7WUFDZDtRQUNGO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUMsa0JBQWtCLE9BQU9DO1FBQzdCLG1DQUFtQztRQUNuQ0EsRUFBRUMsY0FBYztRQUNoQixNQUFNQyxXQUFXLElBQUlDO1FBQ3JCRCxTQUFTRSxNQUFNLENBQUMsU0FBUzlDO1FBQ3pCLE1BQU0rQyxXQUFXLE1BQU1uRCw2Q0FBS0EsQ0FBQ29ELElBQUksQ0FBQyxjQUFjSjtRQUNoRCxJQUFJRyxTQUFTRSxJQUFJLENBQUNDLE9BQU8sRUFBRTtZQUN6QnZELGlEQUFLQSxDQUFDdUQsT0FBTyxDQUFDSCxTQUFTRSxJQUFJLENBQUNFLEdBQUc7WUFDL0JsRCxTQUFTO1FBQ1gsT0FDSztZQUNITixpREFBS0EsQ0FBQ3lELEtBQUssQ0FBQztRQUNkO0lBQ0Y7SUFFQSxNQUFNQyxvQkFBb0IsQ0FBQ1g7UUFDekI3QixhQUFhO1lBQUMsR0FBR0QsU0FBUztZQUFFLENBQUM4QixFQUFFWSxNQUFNLENBQUNDLElBQUksQ0FBQyxFQUFFYixFQUFFWSxNQUFNLENBQUNFLEtBQUs7UUFBQTtJQUM3RDtJQUVBLE1BQU1DLHVCQUF1QixDQUFDZjtRQUM1QjFCLGdCQUFnQjtZQUFDLEdBQUdELFlBQVk7WUFBRSxDQUFDMkIsRUFBRVksTUFBTSxDQUFDQyxJQUFJLENBQUMsRUFBRWIsRUFBRVksTUFBTSxDQUFDRSxLQUFLO1FBQUE7SUFDbkU7SUFFQSxNQUFNRSxvQkFBb0IsT0FBT2hCO1FBQy9CQSxFQUFFQyxjQUFjO1FBQ2hCLElBQUk7WUFDRixNQUFNSSxXQUFXLE1BQU1uRCw2Q0FBS0EsQ0FBQ29ELElBQUksQ0FBQyxhQUFhO2dCQUM3Q2hELE9BQU9ZLFVBQVVaLEtBQUs7Z0JBQ3RCYyxVQUFVRixVQUFVRSxRQUFRO1lBQzlCO1lBRUEsSUFBSWlDLFNBQVNFLElBQUksQ0FBQ0MsT0FBTyxFQUFFO2dCQUN6QixrQ0FBa0M7Z0JBQ2xDZixhQUFhd0IsT0FBTyxDQUFDLGFBQWFaLFNBQVNFLElBQUksQ0FBQ1csS0FBSyxJQUFJO2dCQUN6RHpCLGFBQWF3QixPQUFPLENBQUMsWUFBWVosU0FBU0UsSUFBSSxDQUFDWSxJQUFJLENBQUMzQyxJQUFJO2dCQUN4RGlCLGFBQWF3QixPQUFPLENBQUMsVUFBVVosU0FBU0UsSUFBSSxDQUFDWSxJQUFJLENBQUNDLEVBQUU7Z0JBQ3BEM0IsYUFBYXdCLE9BQU8sQ0FBQyxzQkFBc0JaLFNBQVNFLElBQUksQ0FBQ1ksSUFBSSxDQUFDRSxjQUFjO2dCQUM1RTVCLGFBQWF3QixPQUFPLENBQUMsWUFBWVosU0FBU0UsSUFBSSxDQUFDWSxJQUFJLENBQUNOLElBQUksSUFBSTtnQkFFNUQsZUFBZTtnQkFDZnBELGNBQWM7Z0JBQ2RFLFlBQVkwQyxTQUFTRSxJQUFJLENBQUNZLElBQUksQ0FBQzNDLElBQUk7Z0JBQ25DRSxzQkFBc0IyQixTQUFTRSxJQUFJLENBQUNZLElBQUksQ0FBQ0UsY0FBYztnQkFDdkR6QyxZQUFZeUIsU0FBU0UsSUFBSSxDQUFDWSxJQUFJLENBQUNOLElBQUksSUFBSTtnQkFDdkNoRCxrQkFBa0I7Z0JBRWxCLHlCQUF5QjtnQkFDekIsSUFBSXdDLFNBQVNFLElBQUksQ0FBQ1ksSUFBSSxDQUFDM0MsSUFBSSxLQUFLLFNBQVM7b0JBQ3ZDdkIsaURBQUtBLENBQUN1RCxPQUFPLENBQUM7b0JBQ2RjLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO2dCQUN6QixPQUFPO29CQUNMdkUsaURBQUtBLENBQUN1RCxPQUFPLENBQUM7b0JBQ2QsdURBQXVEO29CQUN2RGMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Z0JBQ3pCO1lBQ0YsT0FBTztnQkFDTHZFLGlEQUFLQSxDQUFDeUQsS0FBSyxDQUFDO1lBQ2Q7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZGUsUUFBUWYsS0FBSyxDQUFDLGdCQUFnQkE7WUFDOUJ6RCxpREFBS0EsQ0FBQ3lELEtBQUssQ0FBQ0EsTUFBTUwsUUFBUSxFQUFFRSxNQUFNbUIsV0FBVztRQUMvQztJQUNGO0lBRUEsTUFBTUMsdUJBQXVCLE9BQU8zQjtRQUNsQ0EsRUFBRUMsY0FBYztRQUVoQiwyQkFBMkI7UUFDM0IsSUFBSTVCLGFBQWFELFFBQVEsS0FBS0MsYUFBYUUsZUFBZSxFQUFFO1lBQzFEdEIsaURBQUtBLENBQUN5RCxLQUFLLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSTtZQUNGLE1BQU1MLFdBQVcsTUFBTW5ELDZDQUFLQSxDQUFDb0QsSUFBSSxDQUFDLGlCQUFpQjtnQkFDakRoRCxPQUFPZSxhQUFhZixLQUFLO2dCQUN6QmMsVUFBVUMsYUFBYUQsUUFBUTtnQkFDL0JJLE1BQU1ILGFBQWFHLElBQUksQ0FBQyx3Q0FBd0M7WUFDbEU7WUFFQSxJQUFJNkIsU0FBU0UsSUFBSSxDQUFDQyxPQUFPLEVBQUU7Z0JBQ3pCdkQsaURBQUtBLENBQUN1RCxPQUFPLENBQUM7Z0JBQ2QsNEJBQTRCO2dCQUM1QnpDLGlCQUFpQjtnQkFDakIsK0JBQStCO2dCQUMvQkksYUFBYTtvQkFBQyxHQUFHRCxTQUFTO29CQUFFWixPQUFPZSxhQUFhZixLQUFLO2dCQUFBO2dCQUNyRCxzQkFBc0I7Z0JBQ3RCZ0IsZ0JBQWdCO29CQUNkaEIsT0FBTztvQkFDUGMsVUFBVTtvQkFDVkcsaUJBQWlCO29CQUNqQkMsTUFBTTtnQkFDUjtZQUNGLE9BQU87Z0JBQ0x2QixpREFBS0EsQ0FBQ3lELEtBQUssQ0FBQ0wsU0FBU0UsSUFBSSxDQUFDbUIsT0FBTyxJQUFJO1lBQ3ZDO1FBQ0YsRUFBRSxPQUFPaEIsT0FBTztZQUNkZSxRQUFRZixLQUFLLENBQUMsdUJBQXVCQTtZQUNyQ3pELGlEQUFLQSxDQUFDeUQsS0FBSyxDQUFDQSxNQUFNTCxRQUFRLEVBQUVFLE1BQU1tQixXQUFXO1FBQy9DO0lBQ0Y7SUFFQSxNQUFNRSxhQUFhO1FBQ2pCN0QsaUJBQWlCLENBQUNEO0lBQ3BCO0lBRUEsTUFBTStELG9CQUFvQjtRQUN4Qi9DLHFCQUFxQjtRQUNyQmIsdUJBQXVCO0lBQ3pCO0lBRUEsTUFBTTZELHNCQUFzQjtRQUMxQixvQ0FBb0M7UUFDcENyQyxhQUFhc0MsVUFBVSxDQUFDO1FBQ3hCdEMsYUFBYXNDLFVBQVUsQ0FBQztRQUN4QnRDLGFBQWFzQyxVQUFVLENBQUM7UUFDeEJ0QyxhQUFhc0MsVUFBVSxDQUFDO1FBQ3hCdEMsYUFBYXNDLFVBQVUsQ0FBQztRQUV4QixlQUFlO1FBQ2Z0RSxjQUFjO1FBQ2RFLFlBQVk7UUFDWm1CLHFCQUFxQjtRQUVyQixpREFBaUQ7UUFDakQ3QixpREFBS0EsQ0FBQ3VELE9BQU8sQ0FBQztRQUVkLDBDQUEwQztRQUMxQ3dCLFdBQVc7WUFDVFYsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7UUFDekIsR0FBRztJQUNMO0lBRUEsTUFBTVMscUJBQXFCO1FBQ3pCbkQscUJBQXFCO0lBQ3ZCO0lBRUEsTUFBTW9ELHdCQUF3QjtRQUM1QmpFLHVCQUF1QixDQUFDRDtJQUMxQjtJQUVBLHlFQUF5RTtJQUN6RSxNQUFNbUUsa0JBQWtCLENBQUNuQztRQUN2QkEsRUFBRUMsY0FBYztRQUNoQixJQUFJLENBQUNkLGdCQUFnQmlELElBQUksSUFBSTtZQUMzQm5GLGlEQUFLQSxDQUFDeUQsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUNBdEQsY0FBYytCO0lBQ2hCO0lBRUEscUJBQ0UsOERBQUNrRDtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDM0YsbURBQUtBO3dCQUFDNEYsS0FBSzdGLGtEQUFNQSxDQUFDOEYsSUFBSTt3QkFBRUMsT0FBTzt3QkFBS0MsS0FBSTt3QkFBR0osV0FBVTs7Ozs7O2tDQUN0RCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1o5RSwyQkFDQyw4REFBQzZFOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDSztnQ0FDQ0MsU0FBUyxJQUFNdkYsT0FBT3dGLElBQUksQ0FBQztnQ0FDM0JQLFdBQVU7O2tEQUVWLDhEQUFDM0YsbURBQUtBO3dDQUNKNEYsS0FBSzlEO3dDQUNMZ0UsT0FBTzt3Q0FDUEssUUFBUTt3Q0FDUkosS0FBSTt3Q0FDSkosV0FBVTs7Ozs7O2tEQUVaLDhEQUFDUztrREFBTXBFLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTXZCLDhEQUFDZ0U7NEJBQ0NDLFNBQVMsSUFBTS9FLGtCQUFrQjs0QkFDakN5RSxXQUFVOztnQ0FDWDs4Q0FDYSw4REFBQzNGLG1EQUFLQTtvQ0FBQzRGLEtBQUs3RixrREFBTUEsQ0FBQ3NHLEtBQUs7b0NBQUVOLEtBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT2pEOUUsZ0NBQ0MsOERBQUN5RTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDVzs0QkFBR1gsV0FBVTtzQ0FBMkJ4RSxnQkFBZ0IsYUFBYTs7Ozs7O3dCQUVyRUEsZ0JBQ0Msb0JBQW9CO3NDQUNwQiw4REFBQ29GOzRCQUFLQyxVQUFVeEI7OzhDQUNkLDhEQUFDVTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNjOzRDQUFNZCxXQUFVO3NEQUEyQjs7Ozs7O3NEQUM1Qyw4REFBQ2U7NENBQ0NDLE1BQUs7NENBQ0x6QyxNQUFLOzRDQUNMQyxPQUFPekMsYUFBYWYsS0FBSzs0Q0FDekJpRyxVQUFVeEM7NENBQ1Z1QixXQUFVOzRDQUNWa0IsUUFBUTs7Ozs7Ozs7Ozs7OzhDQUdaLDhEQUFDbkI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDYzs0Q0FBTWQsV0FBVTtzREFBMkI7Ozs7OztzREFDNUMsOERBQUNlOzRDQUNDQyxNQUFLOzRDQUNMekMsTUFBSzs0Q0FDTEMsT0FBT3pDLGFBQWFELFFBQVE7NENBQzVCbUYsVUFBVXhDOzRDQUNWdUIsV0FBVTs0Q0FDVmtCLFFBQVE7Ozs7Ozs7Ozs7Ozs4Q0FHWiw4REFBQ25CO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2M7NENBQU1kLFdBQVU7c0RBQTJCOzs7Ozs7c0RBQzVDLDhEQUFDZTs0Q0FDQ0MsTUFBSzs0Q0FDTHpDLE1BQUs7NENBQ0xDLE9BQU96QyxhQUFhRSxlQUFlOzRDQUNuQ2dGLFVBQVV4Qzs0Q0FDVnVCLFdBQVU7NENBQ1ZrQixRQUFROzs7Ozs7Ozs7Ozs7OENBR1osOERBQUNuQjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNLOzRDQUNDVyxNQUFLOzRDQUNMaEIsV0FBVTtzREFDWDs7Ozs7O3NEQUdELDhEQUFDSzs0Q0FDQ1csTUFBSzs0Q0FDTFYsU0FBUyxJQUFNL0Usa0JBQWtCOzRDQUNqQ3lFLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs4Q0FJSCw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNtQjt3Q0FBRW5CLFdBQVU7OzRDQUF3Qjs0Q0FDVjswREFDekIsOERBQUNLO2dEQUNDVyxNQUFLO2dEQUNMVixTQUFTaEI7Z0RBQ1RVLFdBQVU7MERBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBT1AsYUFBYTtzQ0FDYiw4REFBQ1k7NEJBQUtDLFVBQVVuQzs7OENBQ2QsOERBQUNxQjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNjOzRDQUFNZCxXQUFVO3NEQUEyQjs7Ozs7O3NEQUM1Qyw4REFBQ2U7NENBQ0NDLE1BQUs7NENBQ0x6QyxNQUFLOzRDQUNMQyxPQUFPNUMsVUFBVVosS0FBSzs0Q0FDdEJpRyxVQUFVNUM7NENBQ1YyQixXQUFVOzRDQUNWa0IsUUFBUTs7Ozs7Ozs7Ozs7OzhDQUdaLDhEQUFDbkI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDYzs0Q0FBTWQsV0FBVTtzREFBMkI7Ozs7OztzREFDNUMsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2U7b0RBQ0NDLE1BQU12RSxlQUFlLFNBQVM7b0RBQzlCOEIsTUFBSztvREFDTEMsT0FBTzVDLFVBQVVFLFFBQVE7b0RBQ3pCbUYsVUFBVTVDO29EQUNWMkIsV0FBVTtvREFDVmtCLFFBQVE7Ozs7Ozs4REFFViw4REFBQ2I7b0RBQ0NXLE1BQUs7b0RBQ0xWLFNBQVNyRDtvREFDVCtDLFdBQVU7OERBRVR2RCxlQUNDLDBCQUEwQjtrRUFDMUIsOERBQUMyRTt3REFBSUMsT0FBTTt3REFBNkJyQixXQUFVO3dEQUFVc0IsU0FBUTt3REFBWUMsTUFBSzs7MEVBQ25GLDhEQUFDQztnRUFBS0MsVUFBUztnRUFBVUMsR0FBRTtnRUFBK1BDLFVBQVM7Ozs7OzswRUFDblMsOERBQUNIO2dFQUFLRSxHQUFFOzs7Ozs7Ozs7OztvRUFHVixxQkFBcUI7a0VBQ3JCLDhEQUFDTjt3REFBSUMsT0FBTTt3REFBNkJyQixXQUFVO3dEQUFVc0IsU0FBUTt3REFBWUMsTUFBSzs7MEVBQ25GLDhEQUFDQztnRUFBS0UsR0FBRTs7Ozs7OzBFQUNSLDhEQUFDRjtnRUFBS0MsVUFBUztnRUFBVUMsR0FBRTtnRUFBMElDLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU14TCw4REFBQzVCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0s7NENBQ0NXLE1BQUs7NENBQ0xoQixXQUFVO3NEQUNYOzs7Ozs7c0RBR0QsOERBQUNLOzRDQUNDVyxNQUFLOzRDQUNMVixTQUFTLElBQU0vRSxrQkFBa0I7NENBQ2pDeUUsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7OzhDQUlILDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ21CO3dDQUFFbkIsV0FBVTs7NENBQXdCOzRDQUNaOzBEQUN2Qiw4REFBQ0s7Z0RBQ0NXLE1BQUs7Z0RBQ0xWLFNBQVNoQjtnREFDVFUsV0FBVTswREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVlkekQsbUNBQ0MsOERBQUN3RDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDNEI7NEJBQUc1QixXQUFVO3NDQUE2Qjs7Ozs7O3NDQUMzQyw4REFBQ21COzRCQUFFbkIsV0FBVTtzQ0FBTzs7Ozs7O3NDQUNwQiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDSztvQ0FDQ0MsU0FBU1g7b0NBQ1RLLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ0s7b0NBQ0NDLFNBQVNkO29DQUNUUSxXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRVCw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDNkI7d0JBQUc3QixXQUFVO2tDQUFtQzs7Ozs7O2tDQUNqRCw4REFBQ21CO3dCQUFFbkIsV0FBVTtrQ0FBa0Q7Ozs7OztrQ0FDL0QsOERBQUNZO3dCQUNDQyxVQUFVbEUsZUFBZWtELGtCQUFrQnBDO3dCQUMzQ3VDLFdBQVU7d0JBQ1Y4QixRQUFPOzswQ0FFUCw4REFBQ2Y7Z0NBQ0NFLFVBQVV0RSxlQUFlLENBQUNlO29DQUN4QlosbUJBQW1CWSxFQUFFWSxNQUFNLENBQUNFLEtBQUs7b0NBQ2pDLElBQUlkLEVBQUVZLE1BQU0sQ0FBQ0UsS0FBSyxLQUFLLElBQUkxRCxjQUFjO2dDQUMzQyxJQUFJLENBQUM0QyxJQUFNekMsU0FBU3lDLEVBQUVZLE1BQU0sQ0FBQ0UsS0FBSztnQ0FDbENBLE9BQU83QixlQUFlRSxrQkFBa0I3QjtnQ0FDeENnRyxNQUFNckUsZUFBZSxTQUFTO2dDQUM5Qm9GLGFBQWFwRixlQUFlLG9CQUFvQjtnQ0FDaERxRCxXQUFVO2dDQUNWa0IsUUFBUTs7Ozs7OzBDQUVWLDhEQUFDYjtnQ0FDQ1csTUFBSztnQ0FDTGhCLFdBQVU7MENBRVRyRCxlQUFlLFdBQVc7Ozs7OzswQ0FFN0IsOERBQUNvRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNLO3dDQUNDVyxNQUFLO3dDQUNMVixTQUFTOzRDQUNQMUQsZ0JBQWdCLENBQUNvRjtnREFDZixJQUFJQSxNQUFNO29EQUNSbEYsbUJBQW1CO29EQUNuQmhDLGNBQWMsS0FBSyx5Q0FBeUM7Z0RBQzlEO2dEQUNBLE9BQU8sQ0FBQ2tIOzRDQUNWO3dDQUNGO3dDQUNBQyxjQUFjLElBQU1qRixlQUFlO3dDQUNuQ2tGLGNBQWMsSUFBTWxGLGVBQWU7d0NBQ25DZ0QsV0FBVTt3Q0FDVm1DLE9BQU87NENBQUVDLFVBQVU7d0NBQU87a0RBRXpCekYsZUFDQyxxQkFBcUI7c0RBQ3JCLDhEQUFDeUU7NENBQUlDLE9BQU07NENBQTZCbEIsT0FBTTs0Q0FBS0ssUUFBTzs0Q0FBS2UsTUFBSzs0Q0FBT0QsU0FBUTs0Q0FBWWUsUUFBTzs0Q0FBZUMsYUFBWTs7OERBQy9ILDhEQUFDQztvREFBS0MsR0FBRTtvREFBSUMsR0FBRTtvREFBSXRDLE9BQU07b0RBQUtLLFFBQU87b0RBQUtrQyxJQUFHO29EQUFJbkIsTUFBSztvREFBT2MsUUFBTztvREFBZUMsYUFBWTs7Ozs7OzhEQUM5Riw4REFBQ0s7b0RBQVNDLFFBQU87b0RBQWlCckIsTUFBSztvREFBT2MsUUFBTztvREFBZUMsYUFBWTs7Ozs7Ozs7Ozs7d0RBR2xGLGNBQWM7c0RBQ2QsOERBQUNsQjs0Q0FBSUMsT0FBTTs0Q0FBNkJsQixPQUFNOzRDQUFLSyxRQUFPOzRDQUFLZSxNQUFLOzRDQUFPRCxTQUFROzRDQUFZZSxRQUFPOzRDQUFlQyxhQUFZOzs4REFDL0gsOERBQUNPO29EQUFPQyxJQUFHO29EQUFLQyxJQUFHO29EQUFLQyxHQUFFO29EQUFJWCxRQUFPO29EQUFlQyxhQUFZO29EQUFJZixNQUFLOzs7Ozs7OERBQ3pFLDhEQUFDMEI7b0RBQUtDLElBQUc7b0RBQUtDLElBQUc7b0RBQUtDLElBQUc7b0RBQVFDLElBQUc7b0RBQVFoQixRQUFPO29EQUFlQyxhQUFZO29EQUFJZ0IsZUFBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBSXJHdkcsNkJBQ0MsOERBQUNnRDt3Q0FBSUMsV0FBVTtrREFDWnJELGVBQWUsNkJBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRN0Q7QUFFQSxpRUFBZTlCLE1BQU1BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQ29tcG9uZW50cy9IZWFkZXIuanN4P2MyYWEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcbmltcG9ydCB7IGFzc2V0cyB9IGZyb20gJ0AvQXNzZXRzL2Fzc2V0cyc7XHJcbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJztcclxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAncmVhY3QtdG9hc3RpZnknO1xyXG5pbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnO1xyXG5cclxuY29uc3QgSGVhZGVyID0gKHsgc2V0U2VhcmNoVGVybSB9KSA9PiB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgW2VtYWlsLCBzZXRFbWFpbF0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbaXNMb2dnZWRJbiwgc2V0SXNMb2dnZWRJbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3VzZXJSb2xlLCBzZXRVc2VyUm9sZV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbc2hvd0xvZ2luTW9kYWwsIHNldFNob3dMb2dpbk1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNSZWdpc3RlcmluZywgc2V0SXNSZWdpc3RlcmluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dBY2NvdW50RHJvcGRvd24sIHNldFNob3dBY2NvdW50RHJvcGRvd25dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtsb2dpbkRhdGEsIHNldExvZ2luRGF0YV0gPSB1c2VTdGF0ZSh7XHJcbiAgICBlbWFpbDogXCJcIixcclxuICAgIHBhc3N3b3JkOiBcIlwiXHJcbiAgfSk7XHJcbiAgY29uc3QgW3JlZ2lzdGVyRGF0YSwgc2V0UmVnaXN0ZXJEYXRhXSA9IHVzZVN0YXRlKHtcclxuICAgIGVtYWlsOiBcIlwiLFxyXG4gICAgcGFzc3dvcmQ6IFwiXCIsXHJcbiAgICBjb25maXJtUGFzc3dvcmQ6IFwiXCIsXHJcbiAgICByb2xlOiBcInVzZXJcIlxyXG4gIH0pO1xyXG4gIGNvbnN0IFt1c2VyUHJvZmlsZVBpY3R1cmUsIHNldFVzZXJQcm9maWxlUGljdHVyZV0gPSB1c2VTdGF0ZShcIi9kZWZhdWx0X3Byb2ZpbGUucG5nXCIpO1xyXG4gIGNvbnN0IFt1c2VyTmFtZSwgc2V0VXNlck5hbWVdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3Nob3dMb2dvdXRDb25maXJtLCBzZXRTaG93TG9nb3V0Q29uZmlybV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dQYXNzd29yZCwgc2V0U2hvd1Bhc3N3b3JkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNTZWFyY2hNb2RlLCBzZXRJc1NlYXJjaE1vZGVdID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW2xvY2FsU2VhcmNoVGVybSwgc2V0TG9jYWxTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtzaG93VG9vbHRpcCwgc2V0U2hvd1Rvb2x0aXBdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAvLyBBZGQgdGhpcyBmdW5jdGlvbiB0byB0b2dnbGUgcGFzc3dvcmQgdmlzaWJpbGl0eVxyXG4gIGNvbnN0IHRvZ2dsZVBhc3N3b3JkVmlzaWJpbGl0eSA9ICgpID0+IHtcclxuICAgIHNldFNob3dQYXNzd29yZCghc2hvd1Bhc3N3b3JkKTtcclxuICB9O1xyXG5cclxuICAvLyBDaGVjayBpZiB1c2VyIGlzIGxvZ2dlZCBpbiBvbiBjb21wb25lbnQgbW91bnRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgYXV0aFRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1dGhUb2tlbicpO1xyXG4gICAgY29uc3Qgc3RvcmVkVXNlclJvbGUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlclJvbGUnKTtcclxuICAgIGNvbnN0IHN0b3JlZFVzZXJJZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VySWQnKTtcclxuICAgIGNvbnN0IHN0b3JlZFByb2ZpbGVQaWN0dXJlID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXJQcm9maWxlUGljdHVyZScpO1xyXG4gICAgY29uc3Qgc3RvcmVkVXNlck5hbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlck5hbWUnKTtcclxuICAgIFxyXG4gICAgaWYgKGF1dGhUb2tlbikge1xyXG4gICAgICBzZXRJc0xvZ2dlZEluKHRydWUpO1xyXG4gICAgICBzZXRVc2VyUm9sZShzdG9yZWRVc2VyUm9sZSB8fCBcInVzZXJcIik7XHJcbiAgICAgIGlmIChzdG9yZWRQcm9maWxlUGljdHVyZSkge1xyXG4gICAgICAgIHNldFVzZXJQcm9maWxlUGljdHVyZShzdG9yZWRQcm9maWxlUGljdHVyZSk7XHJcbiAgICAgIH1cclxuICAgICAgaWYgKHN0b3JlZFVzZXJOYW1lKSB7XHJcbiAgICAgICAgc2V0VXNlck5hbWUoc3RvcmVkVXNlck5hbWUpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBvblN1Ym1pdEhhbmRsZXIgPSBhc3luYyAoZSkgPT4ge1xyXG4gICAgLy8gRXhpc3RpbmcgZW1haWwgc3Vic2NyaXB0aW9uIGNvZGVcclxuICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoXCJlbWFpbFwiLCBlbWFpbCk7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoJy9hcGkvZW1haWwnLCBmb3JtRGF0YSk7XHJcbiAgICBpZiAocmVzcG9uc2UuZGF0YS5zdWNjZXNzKSB7XHJcbiAgICAgIHRvYXN0LnN1Y2Nlc3MocmVzcG9uc2UuZGF0YS5tc2cpO1xyXG4gICAgICBzZXRFbWFpbChcIlwiKTtcclxuICAgIH1cclxuICAgIGVsc2Uge1xyXG4gICAgICB0b2FzdC5lcnJvcihcIkVycm9yXCIpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVMb2dpbkNoYW5nZSA9IChlKSA9PiB7XHJcbiAgICBzZXRMb2dpbkRhdGEoey4uLmxvZ2luRGF0YSwgW2UudGFyZ2V0Lm5hbWVdOiBlLnRhcmdldC52YWx1ZX0pO1xyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlUmVnaXN0ZXJDaGFuZ2UgPSAoZSkgPT4ge1xyXG4gICAgc2V0UmVnaXN0ZXJEYXRhKHsuLi5yZWdpc3RlckRhdGEsIFtlLnRhcmdldC5uYW1lXTogZS50YXJnZXQudmFsdWV9KTtcclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZUxvZ2luU3VibWl0ID0gYXN5bmMgKGUpID0+IHtcclxuICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdCgnL2FwaS9hdXRoJywge1xyXG4gICAgICAgIGVtYWlsOiBsb2dpbkRhdGEuZW1haWwsXHJcbiAgICAgICAgcGFzc3dvcmQ6IGxvZ2luRGF0YS5wYXNzd29yZFxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcclxuICAgICAgICAvLyBTdG9yZSBhdXRoIGRhdGEgaW4gbG9jYWxTdG9yYWdlXHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2F1dGhUb2tlbicsIHJlc3BvbnNlLmRhdGEudG9rZW4gfHwgJ2R1bW15LXRva2VuJyk7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3VzZXJSb2xlJywgcmVzcG9uc2UuZGF0YS51c2VyLnJvbGUpO1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VySWQnLCByZXNwb25zZS5kYXRhLnVzZXIuaWQpO1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VyUHJvZmlsZVBpY3R1cmUnLCByZXNwb25zZS5kYXRhLnVzZXIucHJvZmlsZVBpY3R1cmUpO1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VyTmFtZScsIHJlc3BvbnNlLmRhdGEudXNlci5uYW1lIHx8ICcnKTtcclxuICAgICAgICBcclxuICAgICAgICAvLyBVcGRhdGUgc3RhdGVcclxuICAgICAgICBzZXRJc0xvZ2dlZEluKHRydWUpO1xyXG4gICAgICAgIHNldFVzZXJSb2xlKHJlc3BvbnNlLmRhdGEudXNlci5yb2xlKTtcclxuICAgICAgICBzZXRVc2VyUHJvZmlsZVBpY3R1cmUocmVzcG9uc2UuZGF0YS51c2VyLnByb2ZpbGVQaWN0dXJlKTtcclxuICAgICAgICBzZXRVc2VyTmFtZShyZXNwb25zZS5kYXRhLnVzZXIubmFtZSB8fCAnJyk7XHJcbiAgICAgICAgc2V0U2hvd0xvZ2luTW9kYWwoZmFsc2UpO1xyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgYWRtaW5cclxuICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS51c2VyLnJvbGUgPT09ICdhZG1pbicpIHtcclxuICAgICAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJMb2dpbiBzdWNjZXNzZnVsXCIpO1xyXG4gICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSBcIi9hZG1pblwiO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICB0b2FzdC5zdWNjZXNzKFwiTG9naW4gc3VjY2Vzc2Z1bFwiKTtcclxuICAgICAgICAgIC8vIFJlZGlyZWN0IHJlZ3VsYXIgdXNlcnMgdG8gaG9tZXBhZ2Ugb3IgdXNlciBkYXNoYm9hcmRcclxuICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gXCIvXCI7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHRvYXN0LmVycm9yKFwiSW52YWxpZCBjcmVkZW50aWFsc1wiKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkxvZ2luIGVycm9yOlwiLCBlcnJvcik7XHJcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IFwiTG9naW4gZmFpbGVkXCIpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlUmVnaXN0ZXJTdWJtaXQgPSBhc3luYyAoZSkgPT4ge1xyXG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgXHJcbiAgICAvLyBWYWxpZGF0ZSBwYXNzd29yZHMgbWF0Y2hcclxuICAgIGlmIChyZWdpc3RlckRhdGEucGFzc3dvcmQgIT09IHJlZ2lzdGVyRGF0YS5jb25maXJtUGFzc3dvcmQpIHtcclxuICAgICAgdG9hc3QuZXJyb3IoXCJQYXNzd29yZHMgZG8gbm90IG1hdGNoXCIpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdCgnL2FwaS9yZWdpc3RlcicsIHtcclxuICAgICAgICBlbWFpbDogcmVnaXN0ZXJEYXRhLmVtYWlsLFxyXG4gICAgICAgIHBhc3N3b3JkOiByZWdpc3RlckRhdGEucGFzc3dvcmQsXHJcbiAgICAgICAgcm9sZTogcmVnaXN0ZXJEYXRhLnJvbGUgLy8gQWx3YXlzIFwidXNlclwiIGZvciBwdWJsaWMgcmVnaXN0cmF0aW9uXHJcbiAgICAgIH0pO1xyXG4gICAgICBcclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xyXG4gICAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJSZWdpc3RyYXRpb24gc3VjY2Vzc2Z1bCEgUGxlYXNlIGxvZ2luLlwiKTtcclxuICAgICAgICAvLyBTd2l0Y2ggYmFjayB0byBsb2dpbiBmb3JtXHJcbiAgICAgICAgc2V0SXNSZWdpc3RlcmluZyhmYWxzZSk7XHJcbiAgICAgICAgLy8gUHJlLWZpbGwgZW1haWwgaW4gbG9naW4gZm9ybVxyXG4gICAgICAgIHNldExvZ2luRGF0YSh7Li4ubG9naW5EYXRhLCBlbWFpbDogcmVnaXN0ZXJEYXRhLmVtYWlsfSk7XHJcbiAgICAgICAgLy8gUmVzZXQgcmVnaXN0ZXIgZm9ybVxyXG4gICAgICAgIHNldFJlZ2lzdGVyRGF0YSh7XHJcbiAgICAgICAgICBlbWFpbDogXCJcIixcclxuICAgICAgICAgIHBhc3N3b3JkOiBcIlwiLFxyXG4gICAgICAgICAgY29uZmlybVBhc3N3b3JkOiBcIlwiLFxyXG4gICAgICAgICAgcm9sZTogXCJ1c2VyXCJcclxuICAgICAgICB9KTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgXCJSZWdpc3RyYXRpb24gZmFpbGVkXCIpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiUmVnaXN0cmF0aW9uIGVycm9yOlwiLCBlcnJvcik7XHJcbiAgICAgIHRvYXN0LmVycm9yKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IFwiUmVnaXN0cmF0aW9uIGZhaWxlZFwiKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IHRvZ2dsZUZvcm0gPSAoKSA9PiB7XHJcbiAgICBzZXRJc1JlZ2lzdGVyaW5nKCFpc1JlZ2lzdGVyaW5nKTtcclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZUxvZ291dENsaWNrID0gKCkgPT4ge1xyXG4gICAgc2V0U2hvd0xvZ291dENvbmZpcm0odHJ1ZSk7XHJcbiAgICBzZXRTaG93QWNjb3VudERyb3Bkb3duKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVMb2dvdXRDb25maXJtID0gKCkgPT4ge1xyXG4gICAgLy8gQ2xlYXIgYXV0aCBkYXRhIGZyb20gbG9jYWxTdG9yYWdlXHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYXV0aFRva2VuJyk7XHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlclJvbGUnKTtcclxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VySWQnKTtcclxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyUHJvZmlsZVBpY3R1cmUnKTtcclxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyTmFtZScpO1xyXG4gICAgXHJcbiAgICAvLyBVcGRhdGUgc3RhdGVcclxuICAgIHNldElzTG9nZ2VkSW4oZmFsc2UpO1xyXG4gICAgc2V0VXNlclJvbGUoXCJcIik7XHJcbiAgICBzZXRTaG93TG9nb3V0Q29uZmlybShmYWxzZSk7XHJcbiAgICBcclxuICAgIC8vIFNob3cgdG9hc3QgYW5kIHdhaXQgYnJpZWZseSBiZWZvcmUgcmVkaXJlY3RpbmdcclxuICAgIHRvYXN0LnN1Y2Nlc3MoXCJMb2dnZWQgb3V0IHN1Y2Nlc3NmdWxseVwiKTtcclxuICAgIFxyXG4gICAgLy8gQWRkIGEgc21hbGwgZGVsYXkgYmVmb3JlIGFueSBuYXZpZ2F0aW9uXHJcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSBcIi9cIjtcclxuICAgIH0sIDMwMCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlTG9nb3V0Q2FuY2VsID0gKCkgPT4ge1xyXG4gICAgc2V0U2hvd0xvZ291dENvbmZpcm0oZmFsc2UpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHRvZ2dsZUFjY291bnREcm9wZG93biA9ICgpID0+IHtcclxuICAgIHNldFNob3dBY2NvdW50RHJvcGRvd24oIXNob3dBY2NvdW50RHJvcGRvd24pO1xyXG4gIH1cclxuXHJcbiAgLy8gU2VhcmNoIGhhbmRsZXIgKGZvciBub3csIGp1c3QgYWxlcnQgb3IgbG9nLCB5b3UgY2FuIHdpcmUgdG8gYmxvZyBsaXN0KVxyXG4gIGNvbnN0IG9uU2VhcmNoSGFuZGxlciA9IChlKSA9PiB7XHJcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICBpZiAoIWxvY2FsU2VhcmNoVGVybS50cmltKCkpIHtcclxuICAgICAgdG9hc3QuZXJyb3IoXCJQbGVhc2UgZW50ZXIgYSBzZWFyY2ggdGVybVwiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG4gICAgc2V0U2VhcmNoVGVybShsb2NhbFNlYXJjaFRlcm0pO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT0ncHktNSBweC01IG1kOnB4LTEyIGxnOnB4LTI4Jz5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlcic+XHJcbiAgICAgICAgPEltYWdlIHNyYz17YXNzZXRzLmxvZ299IHdpZHRoPXsxODB9IGFsdD0nJyBjbGFzc05hbWU9J3ctWzEzMHB4XSBzbTp3LWF1dG8nLz5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZmxleCBnYXAtMyc+XHJcbiAgICAgICAgICB7aXNMb2dnZWRJbiA/IChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgICAgIDxidXR0b24gXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL3Byb2ZpbGUnKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0nZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgZm9udC1tZWRpdW0gcHktMSBweC0zIHNtOnB5LTMgc206cHgtNiBib3JkZXIgYm9yZGVyLXNvbGlkIGJvcmRlci1ibGFjayBzaGFkb3ctWy03cHhfN3B4XzBweF8jMDAwMDAwXSdcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8SW1hZ2UgXHJcbiAgICAgICAgICAgICAgICAgIHNyYz17dXNlclByb2ZpbGVQaWN0dXJlfSBcclxuICAgICAgICAgICAgICAgICAgd2lkdGg9ezI0fSBcclxuICAgICAgICAgICAgICAgICAgaGVpZ2h0PXsyNH0gXHJcbiAgICAgICAgICAgICAgICAgIGFsdD1cIkFjY291bnRcIiBcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy02IGgtNiByb3VuZGVkLWZ1bGwgb2JqZWN0LWNvdmVyXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8c3Bhbj57dXNlck5hbWUgfHwgXCJBY2NvdW50XCJ9PC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIHsvKiBSZW1vdmVkIGRyb3Bkb3duIG1lbnUgKi99XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgPGJ1dHRvbiBcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93TG9naW5Nb2RhbCh0cnVlKX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGZvbnQtbWVkaXVtIHB5LTEgcHgtMyBzbTpweS0zIHNtOnB4LTYgYm9yZGVyIGJvcmRlci1zb2xpZCBib3JkZXItYmxhY2sgc2hhZG93LVstN3B4XzdweF8wcHhfIzAwMDAwMF0nXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBHZXQgc3RhcnRlZCA8SW1hZ2Ugc3JjPXthc3NldHMuYXJyb3d9IGFsdD1cIlwiIC8+XHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIFxyXG4gICAgICB7LyogTG9naW4vUmVnaXN0ZXIgTW9kYWwgKi99XHJcbiAgICAgIHtzaG93TG9naW5Nb2RhbCAmJiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgei01MFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBwLTggcm91bmRlZC1tZCBzaGFkb3ctbGcgdy05NlwiPlxyXG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTRcIj57aXNSZWdpc3RlcmluZyA/IFwiUmVnaXN0ZXJcIiA6IFwiTG9naW5cIn08L2gyPlxyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAge2lzUmVnaXN0ZXJpbmcgPyAoXHJcbiAgICAgICAgICAgICAgLy8gUmVnaXN0cmF0aW9uIEZvcm1cclxuICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlUmVnaXN0ZXJTdWJtaXR9PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LWdyYXktNzAwIG1iLTJcIj5FbWFpbDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgIDxpbnB1dCBcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZW1haWxcIiBcclxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwiZW1haWxcIlxyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyZWdpc3RlckRhdGEuZW1haWx9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZVJlZ2lzdGVyQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kXCJcclxuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtZ3JheS03MDAgbWItMlwiPlBhc3N3b3JkPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPGlucHV0IFxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiIFxyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwYXNzd29yZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3JlZ2lzdGVyRGF0YS5wYXNzd29yZH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlUmVnaXN0ZXJDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWRcIlxyXG4gICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1ncmF5LTcwMCBtYi0yXCI+Q29uZmlybSBQYXNzd29yZDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgIDxpbnB1dCBcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIiBcclxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwiY29uZmlybVBhc3N3b3JkXCJcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cmVnaXN0ZXJEYXRhLmNvbmZpcm1QYXNzd29yZH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlUmVnaXN0ZXJDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWRcIlxyXG4gICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibGFjayB0ZXh0LXdoaXRlIHB4LTQgcHktMiByb3VuZGVkLW1kXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIFJlZ2lzdGVyXHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uIFxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dMb2dpbk1vZGFsKGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHB4LTQgcHktMlwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBDYW5jZWxcclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICBBbHJlYWR5IGhhdmUgYW4gYWNjb3VudD97XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBcclxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlRm9ybX1cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dW5kZXJsaW5lXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICBMb2dpblxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Zvcm0+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgLy8gTG9naW4gRm9ybVxyXG4gICAgICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVMb2dpblN1Ym1pdH0+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtZ3JheS03MDAgbWItMlwiPkVtYWlsPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPGlucHV0IFxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiIFxyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJlbWFpbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2xvZ2luRGF0YS5lbWFpbH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlTG9naW5DaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWRcIlxyXG4gICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1ncmF5LTcwMCBtYi0yXCI+UGFzc3dvcmQ8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGlucHV0IFxyXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd1Bhc3N3b3JkID8gXCJ0ZXh0XCIgOiBcInBhc3N3b3JkXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwicGFzc3dvcmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2xvZ2luRGF0YS5wYXNzd29yZH1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVMb2dpbkNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHByLTEwXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZVBhc3N3b3JkVmlzaWJpbGl0eX1cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LXktMCByaWdodC0wIHByLTMgZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1ncmF5LTYwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAge3Nob3dQYXNzd29yZCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gRXllLXNsYXNoIGljb24gKGhpZGRlbilcclxuICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwiaC01IHctNVwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0zLjcwNyAyLjI5M2ExIDEgMCAwMC0xLjQxNCAxLjQxNGwxNCAxNGExIDEgMCAwMDEuNDE0LTEuNDE0bC0xLjQ3My0xLjQ3M0ExMC4wMTQgMTAuMDE0IDAgMDAxOS41NDIgMTBDMTguMjY4IDUuOTQzIDE0LjQ3OCAzIDEwIDNhOS45NTggOS45NTggMCAwMC00LjUxMiAxLjA3NGwtMS43OC0xLjc4MXptNC4yNjEgNC4yNmwxLjUxNCAxLjUxNWEyLjAwMyAyLjAwMyAwIDAxMi40NSAyLjQ1bDEuNTE0IDEuNTE0YTQgNCAwIDAwLTUuNDc4LTUuNDc4elwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0xMi40NTQgMTYuNjk3TDkuNzUgMTMuOTkyYTQgNCAwIDAxLTMuNzQyLTMuNzQxTDIuMzM1IDYuNTc4QTkuOTggOS45OCAwIDAwLjQ1OCAxMGMxLjI3NCA0LjA1NyA1LjA2NSA3IDkuNTQyIDcgLjg0NyAwIDEuNjY5LS4xMDUgMi40NTQtLjMwM3pcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIEV5ZSBpY29uICh2aXNpYmxlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBjbGFzc05hbWU9XCJoLTUgdy01XCIgdmlld0JveD1cIjAgMCAyMCAyMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTEwIDEyYTIgMiAwIDEwMC00IDIgMiAwIDAwMCA0elwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0uNDU4IDEwQzEuNzMyIDUuOTQzIDUuNTIyIDMgMTAgM3M4LjI2OCAyLjk0MyA5LjU0MiA3Yy0xLjI3NCA0LjA1Ny01LjA2NCA3LTkuNTQyIDdTMS43MzIgMTQuMDU3LjQ1OCAxMHpNMTQgMTBhNCA0IDAgMTEtOCAwIDQgNCAwIDAxOCAwelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibGFjayB0ZXh0LXdoaXRlIHB4LTQgcHktMiByb3VuZGVkLW1kXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIExvZ2luXHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uIFxyXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dMb2dpbk1vZGFsKGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHB4LTQgcHktMlwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBDYW5jZWxcclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICBEb24ndCBoYXZlIGFuIGFjY291bnQ/e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gXHJcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZUZvcm19XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnVuZGVybGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgUmVnaXN0ZXJcclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9mb3JtPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcblxyXG4gICAgICB7LyogTG9nb3V0IENvbmZpcm1hdGlvbiBNb2RhbCAqL31cclxuICAgICAge3Nob3dMb2dvdXRDb25maXJtICYmIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHAtNiByb3VuZGVkLW1kIHNoYWRvdy1sZyBtYXgtdy1zbSB3LWZ1bGxcIj5cclxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi00XCI+Q29uZmlybSBMb2dvdXQ8L2gzPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi02XCI+QXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGxvZyBvdXQ/IFlvdSB3aWxsIG5lZWQgdG8gbG9nIGluIGFnYWluIHRvIGFjY2VzcyB5b3VyIGFjY291bnQuPC9wPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgZ2FwLTNcIj5cclxuICAgICAgICAgICAgICA8YnV0dG9uIFxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTG9nb3V0Q2FuY2VsfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTEwMFwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgQ2FuY2VsXHJcbiAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgPGJ1dHRvbiBcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUxvZ291dENvbmZpcm19XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctYmxhY2sgdGV4dC13aGl0ZSByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktODAwXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBMb2dvdXRcclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPSd0ZXh0LWNlbnRlciBteS04Jz5cclxuICAgICAgICA8aDEgY2xhc3NOYW1lPSd0ZXh0LTN4bCBzbTp0ZXh0LTV4bCBmb250LW1lZGl1bSc+TXIuQmxvZ2dlcjwvaDE+XHJcbiAgICAgICAgPHAgY2xhc3NOYW1lPSdtdC0xMCBtYXgtdy1bNzQwcHhdIG0tYXV0byB0ZXh0LXhzIHNtOnRleHQtYmFzZSc+RGlzY292ZXIgaW5zaWdodGZ1bCBhcnRpY2xlcywgdHJlbmRpbmcgdGVjaCBuZXdzLCBzdGFydHVwIGpvdXJuZXlzLCBhbmQgbGlmZXN0eWxlIHN0b3JpZXMg4oCUIGFsbCBpbiBvbmUgcGxhY2UuIFdlbGNvbWUgdG8geW91ciBkYWlseSBkb3NlIG9mIGluc3BpcmF0aW9uIGFuZCBrbm93bGVkZ2UuPC9wPlxyXG4gICAgICAgIDxmb3JtIFxyXG4gICAgICAgICAgb25TdWJtaXQ9e2lzU2VhcmNoTW9kZSA/IG9uU2VhcmNoSGFuZGxlciA6IG9uU3VibWl0SGFuZGxlcn0gXHJcbiAgICAgICAgICBjbGFzc05hbWU9J2ZsZXgganVzdGlmeS1iZXR3ZWVuIG1heC13LVs1MDBweF0gc2NhbGUtNzUgc206c2NhbGUtMTAwIG14LWF1dG8gbXQtMTAgYm9yZGVyIGJvcmRlci1ibGFjayBzaGFkb3ctWy03cHhfN3B4XzBweF8jMDAwMDAwXSdcclxuICAgICAgICAgIGFjdGlvbj1cIlwiXHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPGlucHV0IFxyXG4gICAgICAgICAgICBvbkNoYW5nZT17aXNTZWFyY2hNb2RlID8gKGUpID0+IHtcclxuICAgICAgICAgICAgICBzZXRMb2NhbFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpO1xyXG4gICAgICAgICAgICAgIGlmIChlLnRhcmdldC52YWx1ZSA9PT0gXCJcIikgc2V0U2VhcmNoVGVybShcIlwiKTtcclxuICAgICAgICAgICAgfSA6IChlKSA9PiBzZXRFbWFpbChlLnRhcmdldC52YWx1ZSl9IFxyXG4gICAgICAgICAgICB2YWx1ZT17aXNTZWFyY2hNb2RlID8gbG9jYWxTZWFyY2hUZXJtIDogZW1haWx9IFxyXG4gICAgICAgICAgICB0eXBlPXtpc1NlYXJjaE1vZGUgPyBcInRleHRcIiA6IFwiZW1haWxcIn0gXHJcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtpc1NlYXJjaE1vZGUgPyAnU2VhcmNoIGJsb2dzLi4uJyA6ICdFbnRlciB5b3VyIGVtYWlsJ30gXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT0ncGwtNCBvdXRsaW5lLW5vbmUgZmxleC0xJyBcclxuICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8YnV0dG9uIFxyXG4gICAgICAgICAgICB0eXBlPSdzdWJtaXQnIFxyXG4gICAgICAgICAgICBjbGFzc05hbWU9J2JvcmRlci1sIGJvcmRlci1ibGFjayBweS00IHB4LTQgc206cHgtOCBhY3RpdmU6YmctZ3JheS02MDAgYWN0aXZlOnRleHQtd2hpdGUnXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIHtpc1NlYXJjaE1vZGUgPyAnU2VhcmNoJyA6ICdTdWJzY3JpYmUnfVxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ncmVsYXRpdmUgZmxleCBpdGVtcy1jZW50ZXInPlxyXG4gICAgICAgICAgICA8YnV0dG9uIFxyXG4gICAgICAgICAgICAgIHR5cGU9J2J1dHRvbicgXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgc2V0SXNTZWFyY2hNb2RlKChwcmV2KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGlmIChwcmV2KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0TG9jYWxTZWFyY2hUZXJtKFwiXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFNlYXJjaFRlcm0oXCJcIik7IC8vIENsZWFyIHBhcmVudCBzZWFyY2ggd2hlbiB0b2dnbGluZyBiYWNrXHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgcmV0dXJuICFwcmV2O1xyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IHNldFNob3dUb29sdGlwKHRydWUpfVxyXG4gICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KCkgPT4gc2V0U2hvd1Rvb2x0aXAoZmFsc2UpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT0nZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHgtNCBib3JkZXItbCBib3JkZXItYmxhY2sgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnMnXHJcbiAgICAgICAgICAgICAgc3R5bGU9e3sgbWluV2lkdGg6ICc1NnB4JyB9fVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAge2lzU2VhcmNoTW9kZSA/IChcclxuICAgICAgICAgICAgICAgIC8vIE1haWwvRW52ZWxvcGUgSWNvblxyXG4gICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgd2lkdGg9XCIyMlwiIGhlaWdodD1cIjIyXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxyZWN0IHg9XCIzXCIgeT1cIjVcIiB3aWR0aD1cIjE4XCIgaGVpZ2h0PVwiMTRcIiByeD1cIjJcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxwb2x5bGluZSBwb2ludHM9XCIzLDcgMTIsMTMgMjEsN1wiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAvLyBTZWFyY2ggSWNvblxyXG4gICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgd2lkdGg9XCIyMlwiIGhlaWdodD1cIjIyXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxjaXJjbGUgY3g9XCIxMVwiIGN5PVwiMTFcIiByPVwiOFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiIGZpbGw9XCJub25lXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPGxpbmUgeDE9XCIyMVwiIHkxPVwiMjFcIiB4Mj1cIjE2LjY1XCIgeTI9XCIxNi42NVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAge3Nob3dUb29sdGlwICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMTAgbGVmdC0xLzIgLXRyYW5zbGF0ZS14LTEvMiBiZy1ibGFjayB0ZXh0LXdoaXRlIHRleHQteHMgcm91bmRlZCBweC0zIHB5LTEgc2hhZG93LWxnIGFuaW1hdGUtZmFkZS1pbiB6LTEwIHdoaXRlc3BhY2Utbm93cmFwXCI+XHJcbiAgICAgICAgICAgICAgICB7aXNTZWFyY2hNb2RlID8gJ1N3aXRjaCB0byBTdWJzY3JpYmUgbW9kZScgOiAnU3dpdGNoIHRvIFNlYXJjaCBtb2RlJ31cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZm9ybT5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IEhlYWRlclxyXG4iXSwibmFtZXMiOlsiYXNzZXRzIiwiSW1hZ2UiLCJMaW5rIiwidXNlUm91dGVyIiwiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInRvYXN0IiwiYXhpb3MiLCJIZWFkZXIiLCJzZXRTZWFyY2hUZXJtIiwicm91dGVyIiwiZW1haWwiLCJzZXRFbWFpbCIsImlzTG9nZ2VkSW4iLCJzZXRJc0xvZ2dlZEluIiwidXNlclJvbGUiLCJzZXRVc2VyUm9sZSIsInNob3dMb2dpbk1vZGFsIiwic2V0U2hvd0xvZ2luTW9kYWwiLCJpc1JlZ2lzdGVyaW5nIiwic2V0SXNSZWdpc3RlcmluZyIsInNob3dBY2NvdW50RHJvcGRvd24iLCJzZXRTaG93QWNjb3VudERyb3Bkb3duIiwibG9naW5EYXRhIiwic2V0TG9naW5EYXRhIiwicGFzc3dvcmQiLCJyZWdpc3RlckRhdGEiLCJzZXRSZWdpc3RlckRhdGEiLCJjb25maXJtUGFzc3dvcmQiLCJyb2xlIiwidXNlclByb2ZpbGVQaWN0dXJlIiwic2V0VXNlclByb2ZpbGVQaWN0dXJlIiwidXNlck5hbWUiLCJzZXRVc2VyTmFtZSIsInNob3dMb2dvdXRDb25maXJtIiwic2V0U2hvd0xvZ291dENvbmZpcm0iLCJzaG93UGFzc3dvcmQiLCJzZXRTaG93UGFzc3dvcmQiLCJpc1NlYXJjaE1vZGUiLCJzZXRJc1NlYXJjaE1vZGUiLCJsb2NhbFNlYXJjaFRlcm0iLCJzZXRMb2NhbFNlYXJjaFRlcm0iLCJzaG93VG9vbHRpcCIsInNldFNob3dUb29sdGlwIiwidG9nZ2xlUGFzc3dvcmRWaXNpYmlsaXR5IiwiYXV0aFRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInN0b3JlZFVzZXJSb2xlIiwic3RvcmVkVXNlcklkIiwic3RvcmVkUHJvZmlsZVBpY3R1cmUiLCJzdG9yZWRVc2VyTmFtZSIsIm9uU3VibWl0SGFuZGxlciIsImUiLCJwcmV2ZW50RGVmYXVsdCIsImZvcm1EYXRhIiwiRm9ybURhdGEiLCJhcHBlbmQiLCJyZXNwb25zZSIsInBvc3QiLCJkYXRhIiwic3VjY2VzcyIsIm1zZyIsImVycm9yIiwiaGFuZGxlTG9naW5DaGFuZ2UiLCJ0YXJnZXQiLCJuYW1lIiwidmFsdWUiLCJoYW5kbGVSZWdpc3RlckNoYW5nZSIsImhhbmRsZUxvZ2luU3VibWl0Iiwic2V0SXRlbSIsInRva2VuIiwidXNlciIsImlkIiwicHJvZmlsZVBpY3R1cmUiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJjb25zb2xlIiwibWVzc2FnZSIsImhhbmRsZVJlZ2lzdGVyU3VibWl0IiwidG9nZ2xlRm9ybSIsImhhbmRsZUxvZ291dENsaWNrIiwiaGFuZGxlTG9nb3V0Q29uZmlybSIsInJlbW92ZUl0ZW0iLCJzZXRUaW1lb3V0IiwiaGFuZGxlTG9nb3V0Q2FuY2VsIiwidG9nZ2xlQWNjb3VudERyb3Bkb3duIiwib25TZWFyY2hIYW5kbGVyIiwidHJpbSIsImRpdiIsImNsYXNzTmFtZSIsInNyYyIsImxvZ28iLCJ3aWR0aCIsImFsdCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJwdXNoIiwiaGVpZ2h0Iiwic3BhbiIsImFycm93IiwiaDIiLCJmb3JtIiwib25TdWJtaXQiLCJsYWJlbCIsImlucHV0IiwidHlwZSIsIm9uQ2hhbmdlIiwicmVxdWlyZWQiLCJwIiwic3ZnIiwieG1sbnMiLCJ2aWV3Qm94IiwiZmlsbCIsInBhdGgiLCJmaWxsUnVsZSIsImQiLCJjbGlwUnVsZSIsImgzIiwiaDEiLCJhY3Rpb24iLCJwbGFjZWhvbGRlciIsInByZXYiLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJzdHlsZSIsIm1pbldpZHRoIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJyZWN0IiwieCIsInkiLCJyeCIsInBvbHlsaW5lIiwicG9pbnRzIiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJsaW5lIiwieDEiLCJ5MSIsIngyIiwieTIiLCJzdHJva2VMaW5lY2FwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Components/Header.jsx\n");

/***/ }),

/***/ "(ssr)/./app/page.jsx":
/*!**********************!*\
  !*** ./app/page.jsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Components_BlogList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Components/BlogList */ \"(ssr)/./Components/BlogList.jsx\");\n/* harmony import */ var _Components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/Components/Footer */ \"(ssr)/./Components/Footer.jsx\");\n/* harmony import */ var _Components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/Components/Header */ \"(ssr)/./Components/Header.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(ssr)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _utils_analyticsUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/analyticsUtils */ \"(ssr)/./utils/analyticsUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        // Set mounted state to prevent hydration mismatch\n        setIsMounted(true);\n        // Track home page view\n        (0,_utils_analyticsUtils__WEBPACK_IMPORTED_MODULE_7__.trackPageView)(\"/\", \"home\");\n    }, []);\n    // Show a simple loading state until client-side hydration is complete\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xl\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\page.jsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\page.jsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_4__.ToastContainer, {\n                theme: \"dark\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\page.jsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                setSearchTerm: setSearchTerm\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\page.jsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_BlogList__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                searchTerm: searchTerm\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\page.jsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\page.jsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.jsx\n");

/***/ }),

/***/ "(ssr)/./utils/analyticsUtils.js":
/*!*********************************!*\
  !*** ./utils/analyticsUtils.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchAnalytics: () => (/* binding */ fetchAnalytics),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   trackPageView: () => (/* binding */ trackPageView)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// Track page view\nconst trackPageView = async (path, contentType = \"page\", blogId = null)=>{\n    try {\n        // For testing purposes, let's track in both dev and prod\n        // Remove this condition to only track in production\n        // if (process.env.NODE_ENV === 'development') {\n        //   console.log('Analytics tracking skipped in development mode');\n        //   return;\n        // }\n        console.log(\"Tracking page view:\", {\n            path,\n            contentType,\n            blogId\n        });\n        // Get referrer\n        const referrer = document.referrer || null;\n        // Send analytics data\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/analytics\", {\n            path,\n            contentType,\n            blogId,\n            referrer\n        });\n        console.log(\"Analytics tracking response:\", response.data);\n    } catch (error) {\n        // Silently fail - don't disrupt user experience for analytics\n        console.error(\"Analytics error:\", error);\n    }\n};\n// Format numbers for display (e.g., 1000 -> 1K)\nconst formatNumber = (num)=>{\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + \"M\";\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + \"K\";\n    }\n    return num.toString();\n};\n// Fetch analytics data (for admin dashboard)\nconst fetchAnalytics = async (period = \"7days\")=>{\n    const token = localStorage.getItem(\"authToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/api/analytics?period=${period}`, {\n        headers: {\n            Authorization: `Bearer ${token}`\n        }\n    });\n    return response.data;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/analyticsUtils.js\n");

/***/ }),

/***/ "(ssr)/./utils/cookieUtils.js":
/*!******************************!*\
  !*** ./utils/cookieUtils.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCookie: () => (/* binding */ getCookie),\n/* harmony export */   hasAcceptedCookies: () => (/* binding */ hasAcceptedCookies),\n/* harmony export */   removeCookie: () => (/* binding */ removeCookie),\n/* harmony export */   setAnalyticsCookies: () => (/* binding */ setAnalyticsCookies),\n/* harmony export */   setCookie: () => (/* binding */ setCookie),\n/* harmony export */   setFunctionalCookies: () => (/* binding */ setFunctionalCookies)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n// Set a cookie\nconst setCookie = (name, value, options = {})=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(name, value, options);\n};\n// Get a cookie\nconst getCookie = (name)=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n};\n// Remove a cookie\nconst removeCookie = (name)=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(name);\n};\n// Check if user has accepted cookies\nconst hasAcceptedCookies = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"cookie-consent\") === \"accepted\";\n};\n// Set analytics cookies (only if user has accepted)\nconst setAnalyticsCookies = ()=>{\n    if (hasAcceptedCookies()) {\n    // Set analytics cookies here\n    // Example: Cookies.set('_ga', 'GA1.2.123456789.1234567890', { expires: 365 })\n    }\n};\n// Set functional cookies (only if user has accepted)\nconst setFunctionalCookies = ()=>{\n    if (hasAcceptedCookies()) {\n    // Set functional cookies here\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/cookieUtils.js\n");

/***/ }),

/***/ "(ssr)/./utils/textUtils.js":
/*!****************************!*\
  !*** ./utils/textUtils.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanDescriptionForPreview: () => (/* binding */ cleanDescriptionForPreview),\n/* harmony export */   getContentPreview: () => (/* binding */ getContentPreview)\n/* harmony export */ });\n// Utility functions for text processing\n/**\n * Clean description for preview by removing image and blog references\n * @param {string} content - The content to clean\n * @returns {string} - Cleaned content\n */ const cleanDescriptionForPreview = (content)=>{\n    if (!content) return \"\";\n    let cleanContent = content;\n    // Remove image references {{image:url|filename}}\n    cleanContent = cleanContent.replace(/\\{\\{image:[^}]+\\}\\}/g, \"\");\n    // Remove blog mentions [[blogId|blogTitle]]\n    cleanContent = cleanContent.replace(/\\[\\[[^\\]]+\\]\\]/g, \"\");\n    // Clean up extra whitespace\n    cleanContent = cleanContent.replace(/\\s+/g, \" \").trim();\n    return cleanContent;\n};\n/**\n * Get a preview of content with a specified length\n * @param {string} content - The content to preview\n * @param {number} maxLength - Maximum length of the preview\n * @returns {string} - Preview content\n */ const getContentPreview = (content, maxLength = 120)=>{\n    const cleanContent = cleanDescriptionForPreview(content);\n    return cleanContent.length > maxLength ? cleanContent.substring(0, maxLength) + \"...\" : cleanContent;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/textUtils.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1db334ffb0c5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vYXBwL2dsb2JhbHMuY3NzP2M5ZWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZGIzMzRmZmIwYzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./Components/CookieConsent.jsx":
/*!**************************************!*\
  !*** ./Components/CookieConsent.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\Components\CookieConsent.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.jsx":
/*!************************!*\
  !*** ./app/layout.jsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.jsx\",\"import\":\"Outfit\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"outfit\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.jsx\\\",\\\"import\\\":\\\"Outfit\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"outfit\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _Components_CookieConsent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/Components/CookieConsent */ \"(rsc)/./Components/CookieConsent.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(rsc)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(rsc)/./node_modules/react-toastify/dist/ReactToastify.css\");\n\n\n\n\n\n\n// In production, use the pre-built CSS file\nconst isProd = \"development\" === \"production\";\nconst cssPath = isProd ? \"/build/tailwind.css\" : \"./globals.css\";\nconst metadata = {\n    title: \"Mr.Blogger\",\n    description: \"A blog platform by Mr.Blogger\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: isProd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: cssPath\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                    lineNumber: 22,\n                    columnNumber: 20\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_5___default().className),\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_CookieConsent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                        position: \"top-center\",\n                        autoClose: 3000\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.jsx\n");

/***/ }),

/***/ "(rsc)/./app/page.jsx":
/*!**********************!*\
  !*** ./app/page.jsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\page.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(ssr)/./Assets/add_icon.png":
/*!*****************************!*\
  !*** ./Assets/add_icon.png ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/add_icon.17426346.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fadd_icon.17426346.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYWRkX2ljb24ucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLGtNQUFrTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYWRkX2ljb24ucG5nP2EzMDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2FkZF9pY29uLjE3NDI2MzQ2LnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFkZF9pY29uLjE3NDI2MzQ2LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/add_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/arrow.png":
/*!**************************!*\
  !*** ./Assets/arrow.png ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/arrow.35bdbbc1.png\",\"height\":16,\"width\":18,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Farrow.35bdbbc1.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":7});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYXJyb3cucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYXJyb3cucG5nPzYxZjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Fycm93LjM1YmRiYmMxLnBuZ1wiLFwiaGVpZ2h0XCI6MTYsXCJ3aWR0aFwiOjE4LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFycm93LjM1YmRiYmMxLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo3fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/arrow.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_icon.png":
/*!******************************!*\
  !*** ./Assets/blog_icon.png ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_icon.6cf97bbc.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_icon.6cf97bbc.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxvTUFBb00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL2Jsb2dfaWNvbi5wbmc/MjJlYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvYmxvZ19pY29uLjZjZjk3YmJjLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfaWNvbi42Y2Y5N2JiYy5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_1.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_1.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_1.4406e300.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_1.4406e300.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18xLnBuZz9lMTk3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18xLjQ0MDZlMzAwLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEuNDQwNmUzMDAucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_1.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_10.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_10.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_10.b87908bf.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_10.b87908bf.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTAucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTAucG5nPzA0NzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEwLmI4NzkwOGJmLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEwLmI4NzkwOGJmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_10.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_11.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_11.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_11.ffa40298.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_11.ffa40298.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTEucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTEucG5nPzAxMDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzExLmZmYTQwMjk4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzExLmZmYTQwMjk4LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_11.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_12.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_12.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_12.e5886225.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_12.e5886225.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTIucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTIucG5nPzQ3ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEyLmU1ODg2MjI1LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEyLmU1ODg2MjI1LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_12.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_13.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_13.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_13.d4a89f0e.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_13.d4a89f0e.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTMucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTMucG5nPzJjNDgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEzLmQ0YTg5ZjBlLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEzLmQ0YTg5ZjBlLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_13.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_14.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_14.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_14.0d239c78.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_14.0d239c78.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTQucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTQucG5nPzMxNzciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE0LjBkMjM5Yzc4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE0LjBkMjM5Yzc4LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_14.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_15.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_15.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_15.1d85ee32.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_15.1d85ee32.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTUucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTUucG5nPzYyOGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE1LjFkODVlZTMyLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE1LjFkODVlZTMyLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_15.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_16.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_16.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_16.ffb19842.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_16.ffb19842.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTYucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTYucG5nPzU5NGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE2LmZmYjE5ODQyLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE2LmZmYjE5ODQyLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_16.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_2.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_2.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_2.b986ae66.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_2.b986ae66.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18yLnBuZz9mZWI1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18yLmI5ODZhZTY2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzIuYjk4NmFlNjYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_2.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_3.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_3.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_3.5e7fff80.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_3.5e7fff80.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMy5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18zLnBuZz9kZWRlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18zLjVlN2ZmZjgwLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzMuNWU3ZmZmODAucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_3.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_4.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_4.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_4.86f96556.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_4.86f96556.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY180LnBuZz8yMGEyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY180Ljg2Zjk2NTU2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzQuODZmOTY1NTYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_4.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_5.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_5.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_5.144896ce.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_5.144896ce.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY181LnBuZz82YjE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY181LjE0NDg5NmNlLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzUuMTQ0ODk2Y2UucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_5.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_6.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_6.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_6.b530ea03.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_6.b530ea03.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY182LnBuZz84ZWQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY182LmI1MzBlYTAzLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzYuYjUzMGVhMDMucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_6.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_7.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_7.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_7.3dcf8c5f.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_7.3dcf8c5f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNy5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY183LnBuZz9mNGJjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY183LjNkY2Y4YzVmLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzcuM2RjZjhjNWYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_7.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_8.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_8.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_8.50101226.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_8.50101226.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfOC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY184LnBuZz85MGQ2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY184LjUwMTAxMjI2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzguNTAxMDEyMjYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_8.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_9.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_9.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_9.56aa9ce8.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_9.56aa9ce8.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfOS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY185LnBuZz9lODQyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY185LjU2YWE5Y2U4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzkuNTZhYTljZTgucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_9.png\n");

/***/ }),

/***/ "(ssr)/./Assets/email_icon.png":
/*!*******************************!*\
  !*** ./Assets/email_icon.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/email_icon.4caec7c6.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Femail_icon.4caec7c6.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZW1haWxfaWNvbi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsc01BQXNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9lbWFpbF9pY29uLnBuZz82MmY1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9lbWFpbF9pY29uLjRjYWVjN2M2LnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmVtYWlsX2ljb24uNGNhZWM3YzYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/email_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/facebook_icon.png":
/*!**********************************!*\
  !*** ./Assets/facebook_icon.png ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/facebook_icon.cbcfc36d.png\",\"height\":58,\"width\":58,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffacebook_icon.cbcfc36d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZmFjZWJvb2tfaWNvbi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsNE1BQTRNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9mYWNlYm9va19pY29uLnBuZz85ZDVmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mYWNlYm9va19pY29uLmNiY2ZjMzZkLnBuZ1wiLFwiaGVpZ2h0XCI6NTgsXCJ3aWR0aFwiOjU4LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmZhY2Vib29rX2ljb24uY2JjZmMzNmQucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/facebook_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/googleplus_icon.png":
/*!************************************!*\
  !*** ./Assets/googleplus_icon.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/googleplus_icon.15e2de32.png\",\"height\":59,\"width\":59,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fgoogleplus_icon.15e2de32.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZ29vZ2xlcGx1c19pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxnTkFBZ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL2dvb2dsZXBsdXNfaWNvbi5wbmc/OWM1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZ29vZ2xlcGx1c19pY29uLjE1ZTJkZTMyLnBuZ1wiLFwiaGVpZ2h0XCI6NTksXCJ3aWR0aFwiOjU5LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmdvb2dsZXBsdXNfaWNvbi4xNWUyZGUzMi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/googleplus_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/logo.png":
/*!*************************!*\
  !*** ./Assets/logo.png ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo.c649e147.png\",\"height\":53,\"width\":186,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.c649e147.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":2});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvbG9nby5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsMkxBQTJMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9sb2dvLnBuZz9jOWE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9sb2dvLmM2NDllMTQ3LnBuZ1wiLFwiaGVpZ2h0XCI6NTMsXCJ3aWR0aFwiOjE4NixcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvLmM2NDllMTQ3LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjoyfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/logo.png\n");

/***/ }),

/***/ "(ssr)/./Assets/logo_light.png":
/*!*******************************!*\
  !*** ./Assets/logo_light.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo_light.9ce1f99e.png\",\"height\":55,\"width\":201,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo_light.9ce1f99e.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":2});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvbG9nb19saWdodC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsdU1BQXVNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9sb2dvX2xpZ2h0LnBuZz83NWI0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9sb2dvX2xpZ2h0LjljZTFmOTllLnBuZ1wiLFwiaGVpZ2h0XCI6NTUsXCJ3aWR0aFwiOjIwMSxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvX2xpZ2h0LjljZTFmOTllLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjoyfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/logo_light.png\n");

/***/ }),

/***/ "(ssr)/./Assets/profile_icon.png":
/*!*********************************!*\
  !*** ./Assets/profile_icon.png ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/profile_icon.fa2679c4.png\",\"height\":92,\"width\":92,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprofile_icon.fa2679c4.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvcHJvZmlsZV9pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywwTUFBME0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL3Byb2ZpbGVfaWNvbi5wbmc/NmI3NyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvcHJvZmlsZV9pY29uLmZhMjY3OWM0LnBuZ1wiLFwiaGVpZ2h0XCI6OTIsXCJ3aWR0aFwiOjkyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnByb2ZpbGVfaWNvbi5mYTI2NzljNC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/profile_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/twitter_icon.png":
/*!*********************************!*\
  !*** ./Assets/twitter_icon.png ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/twitter_icon.0d1dc581.png\",\"height\":59,\"width\":59,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ftwitter_icon.0d1dc581.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvdHdpdHRlcl9pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywwTUFBME0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL3R3aXR0ZXJfaWNvbi5wbmc/NDI5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvdHdpdHRlcl9pY29uLjBkMWRjNTgxLnBuZ1wiLFwiaGVpZ2h0XCI6NTksXCJ3aWR0aFwiOjU5LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnR3aXR0ZXJfaWNvbi4wZDFkYzU4MS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/twitter_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/upload_area.png":
/*!********************************!*\
  !*** ./Assets/upload_area.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/upload_area.1ee5fe3d.png\",\"height\":140,\"width\":240,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fupload_area.1ee5fe3d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvdXBsb2FkX2FyZWEucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDBNQUEwTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvdXBsb2FkX2FyZWEucG5nP2RhZTMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL3VwbG9hZF9hcmVhLjFlZTVmZTNkLnBuZ1wiLFwiaGVpZ2h0XCI6MTQwLFwid2lkdGhcIjoyNDAsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGdXBsb2FkX2FyZWEuMWVlNWZlM2QucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/upload_area.png\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9hcHAvZmF2aWNvbi5pY28/YWVmZiJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/react-toastify","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/supports-color","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();