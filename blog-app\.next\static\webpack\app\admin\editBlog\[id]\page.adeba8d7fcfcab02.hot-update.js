"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/editBlog/[id]/page",{

/***/ "(app-pages-browser)/./app/admin/editBlog/[id]/page.jsx":
/*!******************************************!*\
  !*** ./app/admin/editBlog/[id]/page.jsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(app-pages-browser)/./Assets/assets.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst EditBlogPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    const [currentImage, setCurrentImage] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [authors, setAuthors] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [likesCount, setLikesCount] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        totalViews: 0,\n        uniqueVisitors: 0\n    });\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        author: \"\",\n        authorId: \"\",\n        authorImg: \"/author_img.png\"\n    });\n    const [showBlogSelector, setShowBlogSelector] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [allBlogs, setAllBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    // Image insertion states\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [imageSearchTerm, setImageSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const [imageUploadLoading, setImageUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [selectedImageFile, setSelectedImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null);\n    // Fetch categories function\n    const fetchCategories = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/categories\");\n            if (response.data.success && response.data.categories.length > 0) {\n                setCategories(response.data.categories);\n            } else {\n                console.error(\"No categories found\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load categories\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load categories\");\n        }\n    };\n    // Fetch authors function\n    const fetchAuthors = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/authors\");\n            if (response.data.success && response.data.authors.length > 0) {\n                setAuthors(response.data.authors);\n            } else {\n                console.error(\"No authors found\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load authors\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching authors:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load authors\");\n        }\n    };\n    // Fetch likes count\n    const fetchLikesCount = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/blog/likes?id=\".concat(params.id));\n            if (response.data.success) {\n                setLikesCount(response.data.count);\n            }\n        } catch (error) {\n            console.error(\"Error fetching likes count:\", error);\n        // Don't show error toast for this as it's not critical\n        }\n    };\n    // Fetch blog analytics\n    const fetchBlogAnalytics = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/blog/analytics?id=\".concat(params.id));\n            if (response.data.success) {\n                setAnalytics(response.data.analytics);\n            }\n        } catch (error) {\n            console.error(\"Error fetching blog analytics:\", error);\n        // Don't show error toast for this as it's not critical\n        }\n    };\n    // Fetch all blogs for the selector\n    const fetchAllBlogs = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/blog\");\n            setAllBlogs(response.data.blogs || []);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n        }\n    };\n    // Insert a blog mention at the cursor position\n    const insertBlogMention = (blogId, blogTitle)=>{\n        // Create the mention format: [[blogId|blogTitle]]\n        const mention = \"[[\".concat(blogId, \"|\").concat(blogTitle, \"]]\");\n        // Get the textarea element\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        // Insert the mention at cursor position\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + mention + textAfter\n        });\n        // Close the selector\n        setShowBlogSelector(false);\n        // Focus back on textarea\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + mention.length, cursorPos + mention.length);\n        }, 100);\n    };\n    // Image-related functions\n    const fetchAllImages = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/images\", {\n                params: {\n                    blogId: params.id,\n                    limit: 50\n                }\n            });\n            if (response.data.success) {\n                setAllImages(response.data.images);\n            }\n        } catch (error) {\n            console.error(\"Error fetching images:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to fetch images\");\n        }\n    };\n    const handleImageUpload = async ()=>{\n        if (!selectedImageFile) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Please select an image file\");\n            return;\n        }\n        setImageUploadLoading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"image\", selectedImageFile);\n            formData.append(\"blogId\", params.id);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"/api/upload/image\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Image uploaded successfully\");\n                setSelectedImageFile(null);\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(response.data.message || \"Failed to upload image\");\n            }\n        } catch (error) {\n            console.error(\"Error uploading image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to upload image\");\n        } finally{\n            setImageUploadLoading(false);\n        }\n    };\n    const deleteImage = async (imageId, imageUrl)=>{\n        if (!window.confirm(\"Are you sure you want to delete this image? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].delete(\"/api/images/\".concat(imageId));\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"Image deleted successfully\");\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(response.data.message || \"Failed to delete image\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to delete image\");\n        }\n    };\n    const insertImageReference = (imageUrl, filename)=>{\n        const imageRef = \"{{image:\".concat(imageUrl, \"|\").concat(filename, \"}}\");\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + imageRef + textAfter\n        });\n        setShowImageSelector(false);\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + imageRef.length, cursorPos + imageRef.length);\n        }, 100);\n    };\n    // Fetch the blog data\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const fetchBlog = async ()=>{\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/api/blog\", {\n                    params: {\n                        id: params.id\n                    }\n                });\n                const blog = response.data;\n                setData({\n                    title: blog.title || \"\",\n                    description: blog.description || \"\",\n                    category: blog.category || \"Startup\",\n                    author: blog.author || \"\",\n                    authorId: blog.authorId || \"\",\n                    authorImg: blog.authorImg || \"/author_img.png\"\n                });\n                setCurrentImage(blog.image || \"\");\n                // Fetch categories and authors\n                await fetchCategories();\n                await fetchAuthors();\n                await fetchLikesCount();\n                await fetchBlogAnalytics();\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error fetching blog:\", error);\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load blog data\");\n                router.push(\"/admin/blogList\");\n            }\n        };\n        fetchBlog();\n    }, [\n        params.id,\n        router\n    ]);\n    const onChangeHandler = (event)=>{\n        const name = event.target.name;\n        const value = event.target.value;\n        if (name === \"authorId\") {\n            // Find the selected author\n            const selectedAuthor = authors.find((author)=>author._id === value);\n            if (selectedAuthor) {\n                setData((data)=>({\n                        ...data,\n                        author: selectedAuthor.name,\n                        authorId: selectedAuthor._id,\n                        authorImg: selectedAuthor.image || \"/author_img.png\"\n                    }));\n            }\n        } else {\n            setData((data)=>({\n                    ...data,\n                    [name]: value\n                }));\n        }\n    };\n    const onSubmitHandler = async (e)=>{\n        e.preventDefault();\n        try {\n            const formData = new FormData();\n            formData.append(\"id\", params.id);\n            formData.append(\"title\", data.title);\n            formData.append(\"description\", data.description);\n            formData.append(\"category\", data.category);\n            formData.append(\"author\", data.author);\n            formData.append(\"authorId\", data.authorId);\n            formData.append(\"authorImg\", data.authorImg);\n            // Only append image if a new one was selected\n            if (image) {\n                formData.append(\"image\", image);\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].put(\"/api/blog\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(response.data.msg || \"Blog updated successfully\");\n                router.push(\"/admin/blogList\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(response.data.msg || \"Error updating blog\");\n            }\n        } catch (error) {\n            console.error(\"Error updating blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to update blog\");\n        }\n    };\n    const deleteBlog = async ()=>{\n        if (!window.confirm(\"Are you sure you want to delete this blog? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].delete(\"/api/blog\", {\n                params: {\n                    id: params.id\n                }\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(response.data.msg || \"Blog deleted successfully\");\n                router.push(\"/admin/blogList\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(response.data.msg || \"Error deleting blog\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to delete blog\");\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"pt-5 px-5 sm:pt-12 sm:pl-16\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Loading blog data...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                lineNumber: 338,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n            lineNumber: 337,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: onSubmitHandler,\n            className: \"pt-5 px-5 sm:pt-12 sm:pl-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"Edit Blog\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 347,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            className: \"text-red-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: likesCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"likes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            className: \"text-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: analytics.totalViews\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"views\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 349,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 346,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl\",\n                    children: \"Current thumbnail\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 385,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: currentImage,\n                        width: 200,\n                        height: 120,\n                        alt: \"Current thumbnail\",\n                        className: \"border border-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                        lineNumber: 387,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 386,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl\",\n                    children: \"Upload new thumbnail (optional)\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 396,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    htmlFor: \"image\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        className: \"mt-4 cursor-pointer\",\n                        src: !image ? _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.upload_area : URL.createObjectURL(image),\n                        width: 140,\n                        height: 70,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                        lineNumber: 398,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 397,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    onChange: (e)=>setImage(e.target.files[0]),\n                    type: \"file\",\n                    id: \"image\",\n                    hidden: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 406,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl mt-4\",\n                    children: \"Blog title\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 413,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    name: \"title\",\n                    onChange: onChangeHandler,\n                    value: data.title,\n                    className: \"w-full sm:w-[500px] mt-4 px-4 py-3 border\",\n                    type: \"text\",\n                    placeholder: \"Type here\",\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 414,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl\",\n                            children: \"Blog Description\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 425,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"blog-description\",\n                                name: \"description\",\n                                onChange: onChangeHandler,\n                                value: data.description,\n                                className: \"w-full sm:w-[500px] px-4 py-3 border\",\n                                placeholder: \"Write content here\",\n                                rows: 6,\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                lineNumber: 427,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 426,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 flex items-center flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        fetchAllBlogs();\n                                        setShowBlogSelector(true);\n                                    },\n                                    className: \"text-sm flex items-center text-blue-600 hover:text-blue-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Mention another blog\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        fetchAllImages();\n                                        setShowImageSelector(true);\n                                    },\n                                    className: \"text-sm flex items-center text-green-600 hover:text-green-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        \"Insert image\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Formats: [[blogId|blogTitle]] | \",\n                                            \"{{image:url|filename}}\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 438,\n                            columnNumber: 21\n                        }, undefined),\n                        showBlogSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Select a blog to mention\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowBlogSelector(false),\n                                                className: \"text-gray-500 hover:text-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search blogs...\",\n                                        className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"divide-y\",\n                                        children: allBlogs.filter((blog)=>blog.title.toLowerCase().includes(searchTerm.toLowerCase())).map((blog)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center\",\n                                                onClick: ()=>insertBlogMention(blog._id, blog.title),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 relative mr-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            src: blog.image,\n                                                            alt: blog.title,\n                                                            fill: true,\n                                                            className: \"object-cover rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium\",\n                                                                children: blog.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: blog.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                ]\n                                            }, blog._id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 45\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                lineNumber: 475,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 474,\n                            columnNumber: 25\n                        }, undefined),\n                        showImageSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Insert Image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowImageSelector(false),\n                                                className: \"text-gray-500 hover:text-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 p-4 border rounded-lg bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3\",\n                                                children: \"Upload New Image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        onChange: (e)=>setSelectedImageFile(e.target.files[0]),\n                                                        className: \"flex-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: handleImageUpload,\n                                                        disabled: !selectedImageFile || imageUploadLoading,\n                                                        className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\",\n                                                        children: imageUploadLoading ? \"Uploading...\" : \"Upload\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search images...\",\n                                        className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                        value: imageSearchTerm,\n                                        onChange: (e)=>setImageSearchTerm(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 562,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                        children: allImages.filter((image)=>image.filename.toLowerCase().includes(imageSearchTerm.toLowerCase())).map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"aspect-square relative mb-2\",\n                                                        onClick: ()=>insertImageReference(image.url, image.filename),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: image.url,\n                                                                alt: image.filename,\n                                                                fill: true,\n                                                                className: \"object-cover rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    deleteImage(image._id, image.url);\n                                                                },\n                                                                className: \"absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                title: \"Delete image\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-4 w-4\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    stroke: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 49\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>insertImageReference(image.url, image.filename),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 truncate\",\n                                                                children: image.filename\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 53\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: new Date(image.uploadDate).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 49\n                                                    }, undefined)\n                                                ]\n                                            }, image._id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 45\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    allImages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: \"No images found. Upload your first image above.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                lineNumber: 527,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 526,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 424,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl mt-4\",\n                    children: \"Blog category\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 621,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                    name: \"category\",\n                    onChange: onChangeHandler,\n                    value: data.category,\n                    className: \"w-40 mt-4 px-4 py-3 border text-gray-500\",\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                            value: category.name,\n                            children: category.name\n                        }, category._id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 629,\n                            columnNumber: 25\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 622,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl mt-4\",\n                    children: \"Blog author\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 635,\n                    columnNumber: 17\n                }, undefined),\n                authors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            name: \"authorId\",\n                            onChange: onChangeHandler,\n                            value: data.authorId,\n                            className: \"w-full sm:w-40 px-4 py-3 border text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select an author\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 29\n                                }, undefined),\n                                authors.map((author)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: author._id,\n                                        children: author.name\n                                    }, author._id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 33\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 638,\n                            columnNumber: 25\n                        }, undefined),\n                        data.authorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mt-2 sm:mt-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: data.authorImg,\n                                    alt: data.author,\n                                    className: \"w-10 h-10 rounded-full object-cover border border-gray-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium\",\n                                    children: data.author\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 653,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 637,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-500\",\n                            children: \"No authors available. Please add authors in Settings.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 665,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            name: \"author\",\n                            onChange: onChangeHandler,\n                            value: data.author,\n                            className: \"w-full sm:w-[500px] mt-2 px-4 py-3 border\",\n                            type: \"text\",\n                            placeholder: \"Author name\",\n                            required: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 666,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 664,\n                    columnNumber: 21\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"mt-8 w-40 h-12 bg-black text-white\",\n                            children: \"UPDATE\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 679,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>router.push(\"/admin/blogList\"),\n                            className: \"mt-8 w-40 h-12 border border-black\",\n                            children: \"CANCEL\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                            lineNumber: 686,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n                    lineNumber: 678,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\editBlog\\\\[id]\\\\page.jsx\",\n            lineNumber: 345,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false);\n};\n_s(EditBlogPage, \"RlzVtKmJWrHck9tH18Atwn2vJAs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams\n    ];\n});\n_c = EditBlogPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditBlogPage);\nvar _c;\n$RefreshReg$(_c, \"EditBlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/editBlog/[id]/page.jsx\n"));

/***/ })

});