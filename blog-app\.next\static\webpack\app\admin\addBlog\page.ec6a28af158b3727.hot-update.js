"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/addBlog/page",{

/***/ "(app-pages-browser)/./app/admin/addBlog/page.jsx":
/*!************************************!*\
  !*** ./app/admin/addBlog/page.jsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(app-pages-browser)/./Assets/assets.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _Components_AdminComponents_BlogTableItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/Components/AdminComponents/BlogTableItem */ \"(app-pages-browser)/./Components/AdminComponents/BlogTableItem.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst BlogManagementPage = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"add\"); // 'add' or 'manage'\n    const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [authors, setAuthors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [blogs, setBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        author: \"\",\n        authorId: \"\",\n        authorImg: \"\"\n    });\n    const [showBlogSelector, setShowBlogSelector] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [allBlogs, setAllBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    // Image insertion states\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [allImages, setAllImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [imageSearchTerm, setImageSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [imageUploadLoading, setImageUploadLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedImageFile, setSelectedImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [tempBlogId, setTempBlogId] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        fetchCategories();\n        fetchAuthors();\n        if (activeTab === \"manage\") {\n            fetchBlogs();\n        }\n    }, [\n        activeTab\n    ]);\n    // Generate a temporary blog ID for image uploads when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setTempBlogId(\"temp_\" + Date.now() + \"_\" + Math.random().toString(36).substring(2, 11));\n    }, []);\n    const fetchCategories = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/categories\");\n            if (response.data.success && response.data.categories.length > 0) {\n                setCategories(response.data.categories);\n                setData((prev)=>({\n                        ...prev,\n                        category: response.data.categories[0].name\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No categories found. Please add categories in Settings.\");\n                setCategories([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching categories:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load categories\");\n            setCategories([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAuthors = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/authors\");\n            if (response.data.success && response.data.authors.length > 0) {\n                setAuthors(response.data.authors);\n                setData((prev)=>({\n                        ...prev,\n                        author: response.data.authors[0].name,\n                        authorId: response.data.authors[0]._id,\n                        authorImg: response.data.authors[0].image || \"/author_img.png\"\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No authors found. Please add authors in Settings.\");\n                setAuthors([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching authors:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load authors\");\n            setAuthors([]);\n        }\n    };\n    const fetchBlogs = async ()=>{\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/blog\");\n            setBlogs(response.data.blogs || []);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load blogs\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const onChangeHandler = (e)=>{\n        const { name, value } = e.target;\n        if (name === \"authorId\") {\n            const selectedAuthor = authors.find((author)=>author._id === value);\n            if (selectedAuthor) {\n                setData({\n                    ...data,\n                    author: selectedAuthor.name,\n                    authorId: selectedAuthor._id,\n                    authorImg: selectedAuthor.image || \"/author_img.png\"\n                });\n            }\n        } else {\n            setData({\n                ...data,\n                [name]: value\n            });\n        }\n    };\n    const onSubmitHandler = async (e)=>{\n        e.preventDefault();\n        if (!image) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select a blog thumbnail image\");\n            return;\n        }\n        if (!data.title.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please enter a blog title\");\n            return;\n        }\n        const formData = new FormData();\n        formData.append(\"title\", data.title);\n        formData.append(\"description\", data.description);\n        formData.append(\"category\", data.category);\n        formData.append(\"author\", data.author);\n        formData.append(\"authorId\", data.authorId);\n        formData.append(\"authorImg\", data.authorImg);\n        formData.append(\"image\", image);\n        formData.append(\"tempBlogId\", tempBlogId);\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/blog\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.msg || \"Blog added successfully\");\n                setImage(false);\n                setData({\n                    title: \"\",\n                    description: \"\",\n                    category: categories.length > 0 ? categories[0].name : \"\",\n                    author: authors.length > 0 ? authors[0].name : \"\",\n                    authorId: authors.length > 0 ? authors[0]._id : \"\",\n                    authorImg: authors.length > 0 ? authors[0].image || \"/author_img.png\" : \"/author_img.png\"\n                });\n                // Reset temp blog ID and clear images\n                setTempBlogId(\"temp_\" + Date.now() + \"_\" + Math.random().toString(36).substring(2, 11));\n                setAllImages([]);\n                // Switch to manage tab and refresh blog list\n                setActiveTab(\"manage\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.msg || \"Error adding blog\");\n            }\n        } catch (error) {\n            console.error(\"Error submitting blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add blog\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const deleteBlog = async (mongoId)=>{\n        if (!window.confirm(\"Are you sure you want to delete this blog?\")) {\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].delete(\"/api/blog\", {\n                params: {\n                    id: mongoId\n                }\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.msg || \"Blog deleted successfully\");\n            fetchBlogs();\n        } catch (error) {\n            console.error(\"Error deleting blog:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete blog\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchAllBlogs = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/blog\");\n            setAllBlogs(response.data.blogs || []);\n        } catch (error) {\n            console.error(\"Error fetching blogs:\", error);\n        }\n    };\n    const insertBlogMention = (blogId, blogTitle)=>{\n        const mention = \"[[\".concat(blogId, \"|\").concat(blogTitle, \"]]\");\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + mention + textAfter\n        });\n        setShowBlogSelector(false);\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + mention.length, cursorPos + mention.length);\n        }, 100);\n    };\n    // Image-related functions\n    const fetchAllImages = async ()=>{\n        if (!tempBlogId) return;\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/api/images\", {\n                params: {\n                    blogId: tempBlogId,\n                    limit: 50\n                }\n            });\n            if (response.data.success) {\n                setAllImages(response.data.images);\n            }\n        } catch (error) {\n            console.error(\"Error fetching images:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to fetch images\");\n        }\n    };\n    const handleImageUpload = async ()=>{\n        if (!selectedImageFile) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image file\");\n            return;\n        }\n        setImageUploadLoading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"image\", selectedImageFile);\n            formData.append(\"blogId\", tempBlogId || \"new\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/upload/image\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image uploaded successfully\");\n                setSelectedImageFile(null);\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.message || \"Failed to upload image\");\n            }\n        } catch (error) {\n            console.error(\"Error uploading image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to upload image\");\n        } finally{\n            setImageUploadLoading(false);\n        }\n    };\n    const deleteImage = async (imageId, imageUrl)=>{\n        if (!window.confirm(\"Are you sure you want to delete this image? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].delete(\"/api/images/\".concat(imageId));\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Image deleted successfully\");\n                // Refresh the images list\n                await fetchAllImages();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(response.data.message || \"Failed to delete image\");\n            }\n        } catch (error) {\n            console.error(\"Error deleting image:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete image\");\n        }\n    };\n    const insertImageReference = (imageUrl, filename)=>{\n        const imageRef = \"{{image:\".concat(imageUrl, \"|\").concat(filename, \"}}\");\n        const textarea = document.getElementById(\"blog-description\");\n        const cursorPos = textarea.selectionStart;\n        const textBefore = data.description.substring(0, cursorPos);\n        const textAfter = data.description.substring(cursorPos);\n        setData({\n            ...data,\n            description: textBefore + imageRef + textAfter\n        });\n        setShowImageSelector(false);\n        setTimeout(()=>{\n            textarea.focus();\n            textarea.setSelectionRange(cursorPos + imageRef.length, cursorPos + imageRef.length);\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pt-5 px-5 sm:pt-12 sm:pl-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Blog Management\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 324,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex border-b border-gray-300 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"py-3 px-6 font-medium rounded-t-lg \".concat(activeTab === \"add\" ? \"bg-black text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        onClick: ()=>setActiveTab(\"add\"),\n                        children: \"Add New Blog\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 328,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"py-3 px-6 font-medium rounded-t-lg ml-2 \".concat(activeTab === \"manage\" ? \"bg-black text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                        onClick: ()=>setActiveTab(\"manage\"),\n                        children: \"Manage Blogs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 338,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 327,\n                columnNumber: 13\n            }, undefined),\n            activeTab === \"add\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: onSubmitHandler,\n                className: \"max-w-[800px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Create New Blog Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 354,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium mb-2\",\n                                    children: [\n                                        \"Upload Thumbnail \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 86\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"image\",\n                                    className: \"cursor-pointer block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        className: \"border border-gray-300 rounded-md\",\n                                        src: !image ? _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.upload_area : URL.createObjectURL(image),\n                                        width: 200,\n                                        height: 120,\n                                        alt: \"\",\n                                        style: {\n                                            objectFit: \"cover\",\n                                            height: \"120px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    onChange: (e)=>setImage(e.target.files[0]),\n                                    type: \"file\",\n                                    id: \"image\",\n                                    hidden: true,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 356,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: [\n                                        \"Blog Title \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500\",\n                                            children: \"*\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 90\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    name: \"title\",\n                                    onChange: onChangeHandler,\n                                    value: data.title,\n                                    className: \"w-full px-4 py-3 border rounded-md\",\n                                    type: \"text\",\n                                    placeholder: \"Type here\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 371,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"blog-description\",\n                                        name: \"description\",\n                                        onChange: onChangeHandler,\n                                        value: data.description,\n                                        className: \"w-full px-4 py-3 border rounded-md\",\n                                        placeholder: \"Write content here\",\n                                        rows: 6,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 flex items-center flex-wrap gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                fetchAllBlogs();\n                                                setShowBlogSelector(true);\n                                            },\n                                            className: \"text-sm flex items-center text-blue-600 hover:text-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                \"Mention another blog\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                fetchAllImages();\n                                                setShowImageSelector(true);\n                                            },\n                                            className: \"text-sm flex items-center text-green-600 hover:text-green-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                \"Insert image\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Formats: [[blogId|blogTitle]] | \",\n                                                    \"{{image:url|filename}}\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 29\n                                }, undefined),\n                                showBlogSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Select a blog to mention\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowBlogSelector(false),\n                                                        className: \"text-gray-500 hover:text-gray-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-6 w-6\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search blogs...\",\n                                                className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"divide-y\",\n                                                children: allBlogs.filter((blog)=>blog.title.toLowerCase().includes(searchTerm.toLowerCase())).map((blog)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center\",\n                                                        onClick: ()=>insertBlogMention(blog._id, blog.title),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 relative mr-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    src: blog.image,\n                                                                    alt: blog.title,\n                                                                    fill: true,\n                                                                    className: \"object-cover rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 61\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 57\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: blog.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: blog.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        ]\n                                                    }, blog._id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 53\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 33\n                                }, undefined),\n                                showImageSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: \"Insert Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowImageSelector(false),\n                                                        className: \"text-gray-500 hover:text-gray-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-6 w-6\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 p-4 border rounded-lg bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3\",\n                                                        children: \"Upload New Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 45\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>setSelectedImageFile(e.target.files[0]),\n                                                                className: \"flex-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: handleImageUpload,\n                                                                disabled: !selectedImageFile || imageUploadLoading,\n                                                                className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\",\n                                                                children: imageUploadLoading ? \"Uploading...\" : \"Upload\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 49\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search images...\",\n                                                className: \"w-full px-4 py-2 border rounded-md mb-4\",\n                                                value: imageSearchTerm,\n                                                onChange: (e)=>setImageSearchTerm(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                                children: allImages.filter((image)=>image.filename.toLowerCase().includes(imageSearchTerm.toLowerCase())).map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square relative mb-2\",\n                                                                onClick: ()=>insertImageReference(image.url, image.filename),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                        src: image.url,\n                                                                        alt: image.filename,\n                                                                        fill: true,\n                                                                        className: \"object-cover rounded\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            deleteImage(image._id, image.url);\n                                                                        },\n                                                                        className: \"absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                                                        title: \"Delete image\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"h-4 w-4\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                                lineNumber: 559,\n                                                                                columnNumber: 69\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 65\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 57\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onClick: ()=>insertImageReference(image.url, image.filename),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600 truncate\",\n                                                                        children: image.filename\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 61\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: new Date(image.uploadDate).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 57\n                                                            }, undefined)\n                                                        ]\n                                                    }, image._id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 53\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            allImages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-500\",\n                                                children: \"No images found. Upload your first image above.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 384,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Category\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 29\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading categories...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 33\n                                }, undefined) : categories.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    name: \"category\",\n                                    onChange: onChangeHandler,\n                                    value: data.category,\n                                    className: \"w-full px-4 py-3 border rounded-md text-gray-700\",\n                                    required: true,\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: category.name,\n                                            children: category.name\n                                        }, category._id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 41\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 33\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500\",\n                                    children: \"No categories available. Please add categories in Settings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 581,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Blog Author\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 29\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading authors...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 33\n                                }, undefined) : authors.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            name: \"authorId\",\n                                            onChange: onChangeHandler,\n                                            value: data.authorId,\n                                            className: \"w-full px-4 py-3 border rounded-md text-gray-700\",\n                                            required: true,\n                                            children: authors.map((author)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: author._id,\n                                                    children: author.name\n                                                }, author._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 45\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 37\n                                        }, undefined),\n                                        data.authorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: data.authorImg,\n                                                alt: data.author,\n                                                className: \"w-10 h-10 rounded-full object-cover border border-gray-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 33\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-500\",\n                                    children: \"No authors available. Please add authors in Settings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 604,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"w-full py-3 bg-black text-white rounded-md hover:bg-gray-800 transition-colors\",\n                            disabled: loading || categories.length === 0 || authors.length === 0,\n                            children: loading ? \"Creating...\" : \"Create Blog Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 639,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                    lineNumber: 353,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 352,\n                columnNumber: 17\n            }, undefined),\n            activeTab === \"manage\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[850px] bg-white p-6 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Manage Blog Posts\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 653,\n                        columnNumber: 21\n                    }, undefined),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-black\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 657,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2\",\n                                children: \"Loading blogs...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 658,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 656,\n                        columnNumber: 25\n                    }, undefined) : blogs.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative overflow-x-auto border border-gray-300 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm text-gray-500 table-fixed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Author\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: blogs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_AdminComponents_BlogTableItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            mongoId: item._id,\n                                            title: item.title,\n                                            author: item.author,\n                                            authorImg: item.authorImg,\n                                            date: item.date,\n                                            deleteBlog: deleteBlog\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 41\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 662,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 661,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No blogs found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 696,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"add\"),\n                                className: \"mt-4 text-blue-600 hover:underline\",\n                                children: \"Add your first blog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                                lineNumber: 697,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 695,\n                        columnNumber: 25\n                    }, undefined),\n                    blogs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 text-sm text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Showing \",\n                                blogs.length,\n                                \" blog posts\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                            lineNumber: 709,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                        lineNumber: 708,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n                lineNumber: 652,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\admin\\\\addBlog\\\\page.jsx\",\n        lineNumber: 323,\n        columnNumber: 9\n    }, undefined);\n};\n_s(BlogManagementPage, \"mVnsRmO4WM1PiZXdPtFwv9yUl3Y=\");\n_c = BlogManagementPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BlogManagementPage);\nvar _c;\n$RefreshReg$(_c, \"BlogManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/addBlog/page.jsx\n"));

/***/ })

});