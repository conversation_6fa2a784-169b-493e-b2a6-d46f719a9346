'use client'
import React from 'react';

const PaperRocketAnimation = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-10 overflow-hidden">
      {/* Paper Rocket SVG */}
      <div className="rocket-container">
        <svg 
          className="rocket" 
          width="40" 
          height="40" 
          viewBox="0 0 100 100" 
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Rocket Body */}
          <path 
            d="M50 10 L60 70 L50 75 L40 70 Z" 
            fill="#000000" 
            stroke="#333333" 
            strokeWidth="1"
          />
          
          {/* Rocket Nose */}
          <path 
            d="M50 10 L45 25 L55 25 Z" 
            fill="#000000"
          />
          
          {/* Rocket Wings */}
          <path 
            d="M40 70 L35 80 L40 75 Z" 
            fill="#000000"
          />
          <path 
            d="M60 70 L65 80 L60 75 Z" 
            fill="#000000"
          />
          
          {/* Rocket Window */}
          <circle 
            cx="50" 
            cy="35" 
            r="6" 
            fill="#ffffff" 
            stroke="#000000" 
            strokeWidth="1"
          />
          
          {/* Rocket Details */}
          <rect 
            x="47" 
            y="45" 
            width="6" 
            height="15" 
            fill="#333333"
          />
          
          {/* Flame/Exhaust */}
          <path 
            d="M45 75 L50 85 L55 75" 
            fill="#ff6b35" 
            opacity="0.8"
          />
          <path 
            d="M47 75 L50 82 L53 75" 
            fill="#ffaa00" 
            opacity="0.9"
          />
        </svg>
      </div>

      <style jsx>{`
        .rocket-container {
          position: absolute;
          animation: rocketFlight 15s linear infinite;
        }

        .rocket {
          transform-origin: center;
          filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
        }

        @keyframes rocketFlight {
          0% {
            top: 50%;
            left: -60px;
            transform: translateY(-50%) rotate(45deg);
          }
          
          25% {
            top: 20%;
            left: 25%;
            transform: translateY(-50%) rotate(135deg);
          }
          
          50% {
            top: 50%;
            left: calc(100% + 60px);
            transform: translateY(-50%) rotate(225deg);
          }
          
          75% {
            top: 80%;
            left: 75%;
            transform: translateY(-50%) rotate(315deg);
          }
          
          100% {
            top: 50%;
            left: -60px;
            transform: translateY(-50%) rotate(405deg);
          }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .rocket {
            width: 30px;
            height: 30px;
          }
          
          @keyframes rocketFlight {
            0% {
              top: 60%;
              left: -50px;
              transform: translateY(-50%) rotate(45deg);
            }
            
            25% {
              top: 30%;
              left: 30%;
              transform: translateY(-50%) rotate(135deg);
            }
            
            50% {
              top: 60%;
              left: calc(100% + 50px);
              transform: translateY(-50%) rotate(225deg);
            }
            
            75% {
              top: 70%;
              left: 70%;
              transform: translateY(-50%) rotate(315deg);
            }
            
            100% {
              top: 60%;
              left: -50px;
              transform: translateY(-50%) rotate(405deg);
            }
          }
        }

        /* Pause animation on hover for better UX */
        .rocket-container:hover {
          animation-play-state: paused;
        }

        /* Add subtle pulsing effect to the flame */
        .rocket path[fill="#ff6b35"],
        .rocket path[fill="#ffaa00"] {
          animation: flameFlicker 0.3s ease-in-out infinite alternate;
        }

        @keyframes flameFlicker {
          0% {
            opacity: 0.6;
            transform: scale(0.9);
          }
          100% {
            opacity: 1;
            transform: scale(1.1);
          }
        }
      `}</style>
    </div>
  );
};

export default PaperRocketAnimation;
