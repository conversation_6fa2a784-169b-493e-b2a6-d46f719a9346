'use client'
import { assets } from '@/Assets/assets';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import axios from 'axios';
import PaperRocketAnimation from './PaperRocketAnimation';

const Header = ({ setSearchTerm }) => {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userRole, setUserRole] = useState("");
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const [showAccountDropdown, setShowAccountDropdown] = useState(false);
  const [loginData, setLoginData] = useState({
    email: "",
    password: ""
  });
  const [registerData, setRegisterData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    role: "user"
  });
  const [userProfilePicture, setUserProfilePicture] = useState("/default_profile.png");
  const [userName, setUserName] = useState("");
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showRegisterPassword, setShowRegisterPassword] = useState(false);
  const [showRegisterConfirmPassword, setShowRegisterConfirmPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [isSearchMode, setIsSearchMode] = useState(true);
  const [localSearchTerm, setLocalSearchTerm] = useState("");
  const [showTooltip, setShowTooltip] = useState(false);

  // Add this function to toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleRegisterPasswordVisibility = () => {
    setShowRegisterPassword(!showRegisterPassword);
  };

  const toggleRegisterConfirmPasswordVisibility = () => {
    setShowRegisterConfirmPassword(!showRegisterConfirmPassword);
  };

  // Check if user is logged in on component mount
  useEffect(() => {
    const authToken = localStorage.getItem('authToken');
    const storedUserRole = localStorage.getItem('userRole');
    const storedUserId = localStorage.getItem('userId');
    const storedProfilePicture = localStorage.getItem('userProfilePicture');
    const storedUserName = localStorage.getItem('userName');

    // Check for remembered credentials
    const rememberedEmail = localStorage.getItem('rememberedEmail');
    const rememberedPassword = localStorage.getItem('rememberedPassword');
    const wasRemembered = localStorage.getItem('rememberMe') === 'true';

    if (rememberedEmail && rememberedPassword && wasRemembered) {
      setLoginData({
        email: rememberedEmail,
        password: rememberedPassword
      });
      setRememberMe(true);
    }

    if (authToken) {
      setIsLoggedIn(true);
      setUserRole(storedUserRole || "user");
      if (storedProfilePicture) {
        setUserProfilePicture(storedProfilePicture);
      }
      if (storedUserName) {
        setUserName(storedUserName);
      }
    }
  }, []);

  const onSubmitHandler = async (e) => {
    // Existing email subscription code
    e.preventDefault();
    const formData = new FormData();
    formData.append("email", email);
    const response = await axios.post('/api/email', formData);
    if (response.data.success) {
      toast.success(response.data.msg);
      setEmail("");
    }
    else {
      toast.error("Error")
    }
  }

  const handleLoginChange = (e) => {
    setLoginData({...loginData, [e.target.name]: e.target.value});
  }

  const handleRegisterChange = (e) => {
    setRegisterData({...registerData, [e.target.name]: e.target.value});
  }

  const handleLoginSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.post('/api/auth', {
        email: loginData.email,
        password: loginData.password
      });
      
      if (response.data.success) {
        // Store auth data in localStorage
        localStorage.setItem('authToken', response.data.token || 'dummy-token');
        localStorage.setItem('userRole', response.data.user.role);
        localStorage.setItem('userId', response.data.user.id);
        localStorage.setItem('userProfilePicture', response.data.user.profilePicture);
        localStorage.setItem('userName', response.data.user.name || '');

        // Handle remember me functionality
        if (rememberMe) {
          localStorage.setItem('rememberedEmail', loginData.email);
          localStorage.setItem('rememberedPassword', loginData.password);
          localStorage.setItem('rememberMe', 'true');
        } else {
          localStorage.removeItem('rememberedEmail');
          localStorage.removeItem('rememberedPassword');
          localStorage.removeItem('rememberMe');
        }

        // Update state
        setIsLoggedIn(true);
        setUserRole(response.data.user.role);
        setUserProfilePicture(response.data.user.profilePicture);
        setUserName(response.data.user.name || '');
        setShowLoginModal(false);
        
        // Check if user is admin
        if (response.data.user.role === 'admin') {
          toast.success("Login successful");
          window.location.href = "/admin";
        } else {
          toast.success("Login successful");
          // Redirect regular users to homepage or user dashboard
          window.location.href = "/";
        }
      } else {
        toast.error("Invalid credentials");
      }
    } catch (error) {
      console.error("Login error:", error);
      toast.error(error.response?.data?.message || "Login failed");
    }
  }

  const handleRegisterSubmit = async (e) => {
    e.preventDefault();
    
    // Validate passwords match
    if (registerData.password !== registerData.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }
    
    try {
      const response = await axios.post('/api/register', {
        email: registerData.email,
        password: registerData.password,
        role: registerData.role // Always "user" for public registration
      });
      
      if (response.data.success) {
        toast.success("Registration successful! Please login.");
        // Switch back to login form
        setIsRegistering(false);
        // Pre-fill email in login form
        setLoginData({...loginData, email: registerData.email});
        // Reset register form
        setRegisterData({
          email: "",
          password: "",
          confirmPassword: "",
          role: "user"
        });
      } else {
        toast.error(response.data.message || "Registration failed");
      }
    } catch (error) {
      console.error("Registration error:", error);
      toast.error(error.response?.data?.message || "Registration failed");
    }
  }

  const toggleForm = () => {
    setIsRegistering(!isRegistering);
  }

  const handleLogoutClick = () => {
    setShowLogoutConfirm(true);
    setShowAccountDropdown(false);
  };

  const handleLogoutConfirm = () => {
    // Clear auth data from localStorage
    localStorage.removeItem('authToken');
    localStorage.removeItem('userRole');
    localStorage.removeItem('userId');
    localStorage.removeItem('userProfilePicture');
    localStorage.removeItem('userName');
    
    // Update state
    setIsLoggedIn(false);
    setUserRole("");
    setShowLogoutConfirm(false);
    
    // Show toast and wait briefly before redirecting
    toast.success("Logged out successfully");
    
    // Add a small delay before any navigation
    setTimeout(() => {
      window.location.href = "/";
    }, 300);
  };

  const handleLogoutCancel = () => {
    setShowLogoutConfirm(false);
  };

  const toggleAccountDropdown = () => {
    setShowAccountDropdown(!showAccountDropdown);
  }

  // Search handler (for now, just alert or log, you can wire to blog list)
  const onSearchHandler = (e) => {
    e.preventDefault();
    if (!localSearchTerm.trim()) {
      toast.error("Please enter a search term");
      return;
    }
    setSearchTerm(localSearchTerm);
  };

  return (
    <div className='py-5 px-5 md:px-12 lg:px-28 relative'>
      {/* Paper Rocket Animation */}
      <PaperRocketAnimation />

      <div className='flex justify-between items-center'>
        <Image src={assets.logo} width={180} alt='' className='w-[130px] sm:w-auto'/>
        <div className='flex gap-3'>
          {isLoggedIn ? (
            <div className="relative">
              <button
                onClick={() => router.push('/profile')}
                className='flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]'
              >
                <Image
                  src={userProfilePicture}
                  width={24}
                  height={24}
                  alt="Account"
                  className="w-6 h-6 rounded-full object-cover"
                />
                <span>{userName || "Account"}</span>
              </button>

              {/* Removed dropdown menu */}
            </div>
          ) : (
            <button
              onClick={() => setShowLoginModal(true)}
              className='flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]'
            >
              Get started <Image src={assets.arrow} alt="" />
            </button>
          )}
        </div>
      </div>
      
      {/* Login/Register Modal */}
      {showLoginModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-md shadow-lg w-96">
            <h2 className="text-2xl font-bold mb-4">{isRegistering ? "Register" : "Login"}</h2>
            
            {isRegistering ? (
              // Registration Form
              <form onSubmit={handleRegisterSubmit}>
                <div className="mb-4">
                  <label className="block text-gray-700 mb-2">Email</label>
                  <input 
                    type="email" 
                    name="email"
                    value={registerData.email}
                    onChange={handleRegisterChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div className="mb-4">
                  <label className="block text-gray-700 mb-2">Password</label>
                  <div className="relative">
                    <input
                      type={showRegisterPassword ? "text" : "password"}
                      name="password"
                      value={registerData.password}
                      onChange={handleRegisterChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md pr-10"
                      required
                    />
                    <button
                      type="button"
                      onClick={toggleRegisterPasswordVisibility}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600"
                    >
                      {showRegisterPassword ? (
                        // Eye-slash icon (hidden)
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                          <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                        </svg>
                      ) : (
                        // Eye icon (visible)
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
                <div className="mb-6">
                  <label className="block text-gray-700 mb-2">Confirm Password</label>
                  <div className="relative">
                    <input
                      type={showRegisterConfirmPassword ? "text" : "password"}
                      name="confirmPassword"
                      value={registerData.confirmPassword}
                      onChange={handleRegisterChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md pr-10"
                      required
                    />
                    <button
                      type="button"
                      onClick={toggleRegisterConfirmPasswordVisibility}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600"
                    >
                      {showRegisterConfirmPassword ? (
                        // Eye-slash icon (hidden)
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                          <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                        </svg>
                      ) : (
                        // Eye icon (visible)
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
                <div className="flex justify-between">
                  <button 
                    type="submit"
                    className="bg-black text-white px-4 py-2 rounded-md"
                  >
                    Register
                  </button>
                  <button 
                    type="button"
                    onClick={() => setShowLoginModal(false)}
                    className="text-gray-600 px-4 py-2"
                  >
                    Cancel
                  </button>
                </div>
                <div className="mt-4 text-center">
                  <p className="text-sm text-gray-600">
                    Already have an account?{" "}
                    <button 
                      type="button"
                      onClick={toggleForm}
                      className="text-blue-600 hover:underline"
                    >
                      Login
                    </button>
                  </p>
                </div>
              </form>
            ) : (
              // Login Form
              <form onSubmit={handleLoginSubmit}>
                <div className="mb-4">
                  <label className="block text-gray-700 mb-2">Email</label>
                  <input 
                    type="email" 
                    name="email"
                    value={loginData.email}
                    onChange={handleLoginChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div className="mb-6">
                  <label className="block text-gray-700 mb-2">Password</label>
                  <div className="relative">
                    <input 
                      type={showPassword ? "text" : "password"}
                      name="password"
                      value={loginData.password}
                      onChange={handleLoginChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md pr-10"
                      required
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600"
                    >
                      {showPassword ? (
                        // Eye-slash icon (hidden)
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                          <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                        </svg>
                      ) : (
                        // Eye icon (visible)
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
                <div className="mb-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={rememberMe}
                      onChange={(e) => setRememberMe(e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Remember me</span>
                  </label>
                </div>
                <div className="flex justify-between">
                  <button 
                    type="submit"
                    className="bg-black text-white px-4 py-2 rounded-md"
                  >
                    Login
                  </button>
                  <button 
                    type="button"
                    onClick={() => setShowLoginModal(false)}
                    className="text-gray-600 px-4 py-2"
                  >
                    Cancel
                  </button>
                </div>
                <div className="mt-4 text-center">
                  <p className="text-sm text-gray-600">
                    Don't have an account?{" "}
                    <button 
                      type="button"
                      onClick={toggleForm}
                      className="text-blue-600 hover:underline"
                    >
                      Register
                    </button>
                  </p>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-md shadow-lg max-w-sm w-full">
            <h3 className="text-lg font-semibold mb-4">Confirm Logout</h3>
            <p className="mb-6">Are you sure you want to log out? You will need to log in again to access your account.</p>
            <div className="flex justify-end gap-3">
              <button 
                onClick={handleLogoutCancel}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
              >
                Cancel
              </button>
              <button 
                onClick={handleLogoutConfirm}
                className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      )}

      <div className='text-center my-8'>
        <h1 className='text-3xl sm:text-5xl font-medium'>Mr.Blogger</h1>
        <p className='mt-10 max-w-[740px] m-auto text-xs sm:text-base'>Discover insightful articles, trending tech news, startup journeys, and lifestyle stories — all in one place. Welcome to your daily dose of inspiration and knowledge.</p>
        <form 
          onSubmit={isSearchMode ? onSearchHandler : onSubmitHandler} 
          className='flex justify-between max-w-[500px] scale-75 sm:scale-100 mx-auto mt-10 border border-black shadow-[-7px_7px_0px_#000000]'
          action=""
        >
          <input 
            onChange={isSearchMode ? (e) => {
              setLocalSearchTerm(e.target.value);
              if (e.target.value === "") setSearchTerm("");
            } : (e) => setEmail(e.target.value)} 
            value={isSearchMode ? localSearchTerm : email} 
            type={isSearchMode ? "text" : "email"} 
            placeholder={isSearchMode ? 'Search blogs...' : 'Enter your email'} 
            className='pl-4 outline-none flex-1' 
            required
          />
          <button 
            type='submit' 
            className='border-l border-black py-4 px-4 sm:px-8 active:bg-gray-600 active:text-white'
          >
            {isSearchMode ? 'Search' : 'Subscribe'}
          </button>
          <div className='relative flex items-center'>
            <button 
              type='button' 
              onClick={() => {
                setIsSearchMode((prev) => {
                  if (prev) {
                    setLocalSearchTerm("");
                    setSearchTerm(""); // Clear parent search when toggling back
                  }
                  return !prev;
                });
              }}
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
              className='flex items-center justify-center px-4 border-l border-black hover:bg-gray-100 transition-colors'
              style={{ minWidth: '56px' }}
            >
              {isSearchMode ? (
                // Mail/Envelope Icon
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                  <rect x="3" y="5" width="18" height="14" rx="2" fill="none" stroke="currentColor" strokeWidth="2" />
                  <polyline points="3,7 12,13 21,7" fill="none" stroke="currentColor" strokeWidth="2" />
                </svg>
              ) : (
                // Search Icon
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                  <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2" fill="none" />
                  <line x1="21" y1="21" x2="16.65" y2="16.65" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                </svg>
              )}
            </button>
            {showTooltip && (
              <div className="absolute -top-10 left-1/2 -translate-x-1/2 bg-black text-white text-xs rounded px-3 py-1 shadow-lg animate-fade-in z-10 whitespace-nowrap">
                {isSearchMode ? 'Switch to Subscribe mode' : 'Switch to Search mode'}
              </div>
            )}
          </div>
        </form>
      </div>
    </div>
  )
}

export default Header
