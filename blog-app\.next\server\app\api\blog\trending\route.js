"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/blog/trending/route";
exports.ids = ["app/api/blog/trending/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Ftrending%2Froute&page=%2Fapi%2Fblog%2Ftrending%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Ftrending%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Ftrending%2Froute&page=%2Fapi%2Fblog%2Ftrending%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Ftrending%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_blog_trending_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/blog/trending/route.js */ \"(rsc)/./app/api/blog/trending/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/blog/trending/route\",\n        pathname: \"/api/blog/trending\",\n        filename: \"route\",\n        bundlePath: \"app/api/blog/trending/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\blog\\\\trending\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_blog_trending_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/blog/trending/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Ftrending%2Froute&page=%2Fapi%2Fblog%2Ftrending%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Ftrending%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/blog/trending/route.js":
/*!****************************************!*\
  !*** ./app/api/blog/trending/route.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/BlogModel */ \"(rsc)/./lib/models/BlogModel.js\");\n/* harmony import */ var _lib_models_LikeModel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/models/LikeModel */ \"(rsc)/./lib/models/LikeModel.js\");\n\n\n\n\nasync function GET() {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        // Get all blogs\n        const blogs = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({}).lean();\n        // Get like counts for each blog\n        const blogPromises = blogs.map(async (blog)=>{\n            try {\n                const count = await _lib_models_LikeModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].countDocuments({\n                    blogId: blog._id\n                });\n                return {\n                    ...blog,\n                    likeCount: count\n                };\n            } catch (error) {\n                console.error(`Error counting likes for blog ${blog._id}:`, error);\n                return {\n                    ...blog,\n                    likeCount: 0\n                };\n            }\n        });\n        const blogsWithLikes = await Promise.all(blogPromises);\n        // Sort blogs by like count (descending)\n        const sortedBlogs = blogsWithLikes.sort((a, b)=>b.likeCount - a.likeCount).slice(0, 12); // Increase to 12 blogs\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            blogs: sortedBlogs\n        });\n    } catch (error) {\n        console.error(\"Error fetching trending blogs:\", error);\n        // Fallback to recent blogs if there's an error\n        try {\n            const recentBlogs = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({}).sort({\n                createdAt: -1\n            }).limit(12) // Increase to 12 blogs\n            .lean();\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                blogs: recentBlogs\n            });\n        } catch (fallbackError) {\n            console.error(\"Fallback error:\", fallbackError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Failed to fetch trending blogs\"\n            }, {\n                status: 500\n            });\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/blog/trending/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(\"mongodb+srv://subhashanas:<EMAIL>/blog-app\");\n    console.log(\"DB Connected\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/BlogModel.js":
/*!*********************************!*\
  !*** ./lib/models/BlogModel.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: true\n    },\n    description: {\n        type: String,\n        required: true\n    },\n    category: {\n        type: String,\n        required: true\n    },\n    author: {\n        type: String,\n        required: true\n    },\n    image: {\n        type: String,\n        required: true\n    },\n    authorImg: {\n        type: String,\n        required: true\n    },\n    date: {\n        type: Date,\n        default: Date.now()\n    }\n});\nconst BlogModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).blog || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"blog\", Schema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BlogModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0Jsb2dNb2RlbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsU0FBUyxJQUFJRCx3REFBZSxDQUFDO0lBQy9CRSxPQUFNO1FBQ0ZDLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBQyxhQUFZO1FBQ1JILE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBRSxVQUFTO1FBQ0xKLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBRyxRQUFPO1FBQ0hMLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBSSxPQUFNO1FBQ0ZOLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBSyxXQUFVO1FBQ05QLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBTSxNQUFLO1FBQ0RSLE1BQUtTO1FBQ0xDLFNBQVFELEtBQUtFLEdBQUc7SUFDcEI7QUFDSjtBQUVBLE1BQU1DLFlBQVlmLHdEQUFlLENBQUNpQixJQUFJLElBQUlqQixxREFBYyxDQUFDLFFBQU9DO0FBRWhFLGlFQUFlYyxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi9tb2RlbHMvQmxvZ01vZGVsLmpzPzhmZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gXCJtb25nb29zZVwiO1xuXG5jb25zdCBTY2hlbWEgPSBuZXcgbW9uZ29vc2UuU2NoZW1hKHtcbiAgICB0aXRsZTp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBkZXNjcmlwdGlvbjp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBjYXRlZ29yeTp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBhdXRob3I6e1xuICAgICAgICB0eXBlOlN0cmluZyxcbiAgICAgICAgcmVxdWlyZWQ6dHJ1ZVxuICAgIH0sXG4gICAgaW1hZ2U6e1xuICAgICAgICB0eXBlOlN0cmluZyxcbiAgICAgICAgcmVxdWlyZWQ6dHJ1ZVxuICAgIH0sXG4gICAgYXV0aG9ySW1nOntcbiAgICAgICAgdHlwZTpTdHJpbmcsXG4gICAgICAgIHJlcXVpcmVkOnRydWVcbiAgICB9LFxuICAgIGRhdGU6e1xuICAgICAgICB0eXBlOkRhdGUsXG4gICAgICAgIGRlZmF1bHQ6RGF0ZS5ub3coKVxuICAgIH1cbn0pXG5cbmNvbnN0IEJsb2dNb2RlbCA9IG1vbmdvb3NlLm1vZGVscy5ibG9nIHx8IG1vbmdvb3NlLm1vZGVsKCdibG9nJyxTY2hlbWEpO1xuXG5leHBvcnQgZGVmYXVsdCBCbG9nTW9kZWw7Il0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiU2NoZW1hIiwidGl0bGUiLCJ0eXBlIiwiU3RyaW5nIiwicmVxdWlyZWQiLCJkZXNjcmlwdGlvbiIsImNhdGVnb3J5IiwiYXV0aG9yIiwiaW1hZ2UiLCJhdXRob3JJbWciLCJkYXRlIiwiRGF0ZSIsImRlZmF1bHQiLCJub3ciLCJCbG9nTW9kZWwiLCJtb2RlbHMiLCJibG9nIiwibW9kZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/BlogModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/LikeModel.js":
/*!*********************************!*\
  !*** ./lib/models/LikeModel.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LikeSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Blog\",\n        required: true\n    },\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n// Check if the model is already defined to prevent overwriting\nconst LikeModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Like || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Like\", LikeSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LikeModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0xpa2VNb2RlbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsYUFBYSxJQUFJRCx3REFBZSxDQUFDO0lBQ3JDRyxRQUFRO1FBQ05DLE1BQU1KLHdEQUFlLENBQUNLLEtBQUssQ0FBQ0MsUUFBUTtRQUNwQ0MsS0FBSztRQUNMQyxVQUFVO0lBQ1o7SUFDQUMsUUFBUTtRQUNOTCxNQUFNSix3REFBZSxDQUFDSyxLQUFLLENBQUNDLFFBQVE7UUFDcENDLEtBQUs7UUFDTEMsVUFBVTtJQUNaO0lBQ0FFLFdBQVc7UUFDVE4sTUFBTU87UUFDTkMsU0FBU0QsS0FBS0UsR0FBRztJQUNuQjtBQUNGO0FBRUEsK0RBQStEO0FBQy9ELE1BQU1DLFlBQVlkLHdEQUFlLENBQUNnQixJQUFJLElBQUloQixxREFBYyxDQUFDLFFBQVFDO0FBRWpFLGlFQUFlYSxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi9tb2RlbHMvTGlrZU1vZGVsLmpzP2E0YTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gXCJtb25nb29zZVwiO1xuXG5jb25zdCBMaWtlU2NoZW1hID0gbmV3IG1vbmdvb3NlLlNjaGVtYSh7XG4gIGJsb2dJZDoge1xuICAgIHR5cGU6IG1vbmdvb3NlLlNjaGVtYS5UeXBlcy5PYmplY3RJZCxcbiAgICByZWY6IFwiQmxvZ1wiLFxuICAgIHJlcXVpcmVkOiB0cnVlXG4gIH0sXG4gIHVzZXJJZDoge1xuICAgIHR5cGU6IG1vbmdvb3NlLlNjaGVtYS5UeXBlcy5PYmplY3RJZCxcbiAgICByZWY6IFwiVXNlclwiLFxuICAgIHJlcXVpcmVkOiB0cnVlXG4gIH0sXG4gIGNyZWF0ZWRBdDoge1xuICAgIHR5cGU6IERhdGUsXG4gICAgZGVmYXVsdDogRGF0ZS5ub3dcbiAgfVxufSk7XG5cbi8vIENoZWNrIGlmIHRoZSBtb2RlbCBpcyBhbHJlYWR5IGRlZmluZWQgdG8gcHJldmVudCBvdmVyd3JpdGluZ1xuY29uc3QgTGlrZU1vZGVsID0gbW9uZ29vc2UubW9kZWxzLkxpa2UgfHwgbW9uZ29vc2UubW9kZWwoXCJMaWtlXCIsIExpa2VTY2hlbWEpO1xuXG5leHBvcnQgZGVmYXVsdCBMaWtlTW9kZWw7Il0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiTGlrZVNjaGVtYSIsIlNjaGVtYSIsImJsb2dJZCIsInR5cGUiLCJUeXBlcyIsIk9iamVjdElkIiwicmVmIiwicmVxdWlyZWQiLCJ1c2VySWQiLCJjcmVhdGVkQXQiLCJEYXRlIiwiZGVmYXVsdCIsIm5vdyIsIkxpa2VNb2RlbCIsIm1vZGVscyIsIkxpa2UiLCJtb2RlbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/LikeModel.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Ftrending%2Froute&page=%2Fapi%2Fblog%2Ftrending%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Ftrending%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();