"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./Components/Header.jsx":
/*!*******************************!*\
  !*** ./Components/Header.jsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(app-pages-browser)/./Assets/assets.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Header = (param)=>{\n    let { setSearchTerm } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLoginModal, setShowLoginModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isRegistering, setIsRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showAccountDropdown, setShowAccountDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [loginData, setLoginData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [registerData, setRegisterData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        role: \"user\"\n    });\n    const [userProfilePicture, setUserProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"/default_profile.png\");\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLogoutConfirm, setShowLogoutConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showRegisterPassword, setShowRegisterPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showRegisterConfirmPassword, setShowRegisterConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isSearchMode, setIsSearchMode] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [localSearchTerm, setLocalSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // Add this function to toggle password visibility\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    const toggleRegisterPasswordVisibility = ()=>{\n        setShowRegisterPassword(!showRegisterPassword);\n    };\n    const toggleRegisterConfirmPasswordVisibility = ()=>{\n        setShowRegisterConfirmPassword(!showRegisterConfirmPassword);\n    };\n    // Check if user is logged in on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const authToken = localStorage.getItem(\"authToken\");\n        const storedUserRole = localStorage.getItem(\"userRole\");\n        const storedUserId = localStorage.getItem(\"userId\");\n        const storedProfilePicture = localStorage.getItem(\"userProfilePicture\");\n        const storedUserName = localStorage.getItem(\"userName\");\n        if (authToken) {\n            setIsLoggedIn(true);\n            setUserRole(storedUserRole || \"user\");\n            if (storedProfilePicture) {\n                setUserProfilePicture(storedProfilePicture);\n            }\n            if (storedUserName) {\n                setUserName(storedUserName);\n            }\n        }\n    }, []);\n    const onSubmitHandler = async (e)=>{\n        // Existing email subscription code\n        e.preventDefault();\n        const formData = new FormData();\n        formData.append(\"email\", email);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/email\", formData);\n        if (response.data.success) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(response.data.msg);\n            setEmail(\"\");\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Error\");\n        }\n    };\n    const handleLoginChange = (e)=>{\n        setLoginData({\n            ...loginData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleRegisterChange = (e)=>{\n        setRegisterData({\n            ...registerData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleLoginSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/auth\", {\n                email: loginData.email,\n                password: loginData.password\n            });\n            if (response.data.success) {\n                // Store auth data in localStorage\n                localStorage.setItem(\"authToken\", response.data.token || \"dummy-token\");\n                localStorage.setItem(\"userRole\", response.data.user.role);\n                localStorage.setItem(\"userId\", response.data.user.id);\n                localStorage.setItem(\"userProfilePicture\", response.data.user.profilePicture);\n                localStorage.setItem(\"userName\", response.data.user.name || \"\");\n                // Update state\n                setIsLoggedIn(true);\n                setUserRole(response.data.user.role);\n                setUserProfilePicture(response.data.user.profilePicture);\n                setUserName(response.data.user.name || \"\");\n                setShowLoginModal(false);\n                // Check if user is admin\n                if (response.data.user.role === \"admin\") {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    window.location.href = \"/admin\";\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    // Redirect regular users to homepage or user dashboard\n                    window.location.href = \"/\";\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Invalid credentials\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Login error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Login failed\");\n        }\n    };\n    const handleRegisterSubmit = async (e)=>{\n        e.preventDefault();\n        // Validate passwords match\n        if (registerData.password !== registerData.confirmPassword) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Passwords do not match\");\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/register\", {\n                email: registerData.email,\n                password: registerData.password,\n                role: registerData.role // Always \"user\" for public registration\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Registration successful! Please login.\");\n                // Switch back to login form\n                setIsRegistering(false);\n                // Pre-fill email in login form\n                setLoginData({\n                    ...loginData,\n                    email: registerData.email\n                });\n                // Reset register form\n                setRegisterData({\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\",\n                    role: \"user\"\n                });\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(response.data.message || \"Registration failed\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Registration error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Registration failed\");\n        }\n    };\n    const toggleForm = ()=>{\n        setIsRegistering(!isRegistering);\n    };\n    const handleLogoutClick = ()=>{\n        setShowLogoutConfirm(true);\n        setShowAccountDropdown(false);\n    };\n    const handleLogoutConfirm = ()=>{\n        // Clear auth data from localStorage\n        localStorage.removeItem(\"authToken\");\n        localStorage.removeItem(\"userRole\");\n        localStorage.removeItem(\"userId\");\n        localStorage.removeItem(\"userProfilePicture\");\n        localStorage.removeItem(\"userName\");\n        // Update state\n        setIsLoggedIn(false);\n        setUserRole(\"\");\n        setShowLogoutConfirm(false);\n        // Show toast and wait briefly before redirecting\n        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Logged out successfully\");\n        // Add a small delay before any navigation\n        setTimeout(()=>{\n            window.location.href = \"/\";\n        }, 300);\n    };\n    const handleLogoutCancel = ()=>{\n        setShowLogoutConfirm(false);\n    };\n    const toggleAccountDropdown = ()=>{\n        setShowAccountDropdown(!showAccountDropdown);\n    };\n    // Search handler (for now, just alert or log, you can wire to blog list)\n    const onSearchHandler = (e)=>{\n        e.preventDefault();\n        if (!localSearchTerm.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Please enter a search term\");\n            return;\n        }\n        setSearchTerm(localSearchTerm);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-5 px-5 md:px-12 lg:px-28\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.logo,\n                        width: 180,\n                        alt: \"\",\n                        className: \"w-[130px] sm:w-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/profile\"),\n                                className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: userProfilePicture,\n                                        width: 24,\n                                        height: 24,\n                                        alt: \"Account\",\n                                        className: \"w-6 h-6 rounded-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: userName || \"Account\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowLoginModal(true),\n                            className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]\",\n                            children: [\n                                \"Get started \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.arrow,\n                                    alt: \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 27\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 247,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, undefined),\n            showLoginModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-8 rounded-md shadow-lg w-96\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: isRegistering ? \"Register\" : \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, undefined),\n                        isRegistering ? // Registration Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleRegisterSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: registerData.email,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            name: \"password\",\n                                            value: registerData.password,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            name: \"confirmPassword\",\n                                            value: registerData.confirmPassword,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Already have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 265,\n                            columnNumber: 15\n                        }, undefined) : // Login Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLoginSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: loginData.email,\n                                            onChange: handleLoginChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    name: \"password\",\n                                                    value: loginData.password,\n                                                    onChange: handleLoginChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md pr-10\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: togglePasswordVisibility,\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600\",\n                                                    children: showPassword ? // Eye-slash icon (hidden)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 25\n                                                    }, undefined) : // Eye icon (visible)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Register\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 329,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                    lineNumber: 260,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 259,\n                columnNumber: 9\n            }, undefined),\n            showLogoutConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-md shadow-lg max-w-sm w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Confirm Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-6\",\n                            children: \"Are you sure you want to log out? You will need to log in again to access your account.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutCancel,\n                                    className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutConfirm,\n                                    className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                    lineNumber: 409,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 408,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center my-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl sm:text-5xl font-medium\",\n                        children: \"Mr.Blogger\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 431,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-10 max-w-[740px] m-auto text-xs sm:text-base\",\n                        children: \"Discover insightful articles, trending tech news, startup journeys, and lifestyle stories — all in one place. Welcome to your daily dose of inspiration and knowledge.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: isSearchMode ? onSearchHandler : onSubmitHandler,\n                        className: \"flex justify-between max-w-[500px] scale-75 sm:scale-100 mx-auto mt-10 border border-black shadow-[-7px_7px_0px_#000000]\",\n                        action: \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                onChange: isSearchMode ? (e)=>{\n                                    setLocalSearchTerm(e.target.value);\n                                    if (e.target.value === \"\") setSearchTerm(\"\");\n                                } : (e)=>setEmail(e.target.value),\n                                value: isSearchMode ? localSearchTerm : email,\n                                type: isSearchMode ? \"text\" : \"email\",\n                                placeholder: isSearchMode ? \"Search blogs...\" : \"Enter your email\",\n                                className: \"pl-4 outline-none flex-1\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"border-l border-black py-4 px-4 sm:px-8 active:bg-gray-600 active:text-white\",\n                                children: isSearchMode ? \"Search\" : \"Subscribe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setIsSearchMode((prev)=>{\n                                                if (prev) {\n                                                    setLocalSearchTerm(\"\");\n                                                    setSearchTerm(\"\"); // Clear parent search when toggling back\n                                                }\n                                                return !prev;\n                                            });\n                                        },\n                                        onMouseEnter: ()=>setShowTooltip(true),\n                                        onMouseLeave: ()=>setShowTooltip(false),\n                                        className: \"flex items-center justify-center px-4 border-l border-black hover:bg-gray-100 transition-colors\",\n                                        style: {\n                                            minWidth: \"56px\"\n                                        },\n                                        children: isSearchMode ? // Mail/Envelope Icon\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"22\",\n                                            height: \"22\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                    x: \"3\",\n                                                    y: \"5\",\n                                                    width: \"18\",\n                                                    height: \"14\",\n                                                    rx: \"2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                    points: \"3,7 12,13 21,7\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, undefined) : // Search Icon\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"22\",\n                                            height: \"22\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"11\",\n                                                    cy: \"11\",\n                                                    r: \"8\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    fill: \"none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"21\",\n                                                    y1: \"21\",\n                                                    x2: \"16.65\",\n                                                    y2: \"16.65\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 -translate-x-1/2 bg-black text-white text-xs rounded px-3 py-1 shadow-lg animate-fade-in z-10 whitespace-nowrap\",\n                                        children: isSearchMode ? \"Switch to Subscribe mode\" : \"Switch to Search mode\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 430,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"iexpWvhRv7F7lDRJbJ4iOk3P4RQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Header;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/Header.jsx\n"));

/***/ })

});