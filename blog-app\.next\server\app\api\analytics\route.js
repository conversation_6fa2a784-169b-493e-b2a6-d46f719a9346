"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/route";
exports.ids = ["app/api/analytics/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_analytics_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/analytics/route.js */ \"(rsc)/./app/api/analytics/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/route\",\n        pathname: \"/api/analytics\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\analytics\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_analytics_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/analytics/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZhbmFseXRpY3MlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmFuYWx5dGljcyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmFuYWx5dGljcyUyRnJvdXRlLmpzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNEU1lTJTVDRGVza3RvcCU1Q01yLkJsb2clNUNibG9nLWFwcCU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDMEI7QUFDdkc7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSx1R0FBdUc7QUFDL0c7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUM2Sjs7QUFFN0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLz9iMzY3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXGFwcFxcXFxhcGlcXFxcYW5hbHl0aWNzXFxcXHJvdXRlLmpzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9hbmFseXRpY3Mvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9hbmFseXRpY3NcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2FuYWx5dGljcy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXGFwcFxcXFxhcGlcXFxcYW5hbHl0aWNzXFxcXHJvdXRlLmpzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL2FuYWx5dGljcy9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQsIG9yaWdpbmFsUGF0aG5hbWUsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/analytics/route.js":
/*!************************************!*\
  !*** ./app/api/analytics/route.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/AnalyticsModel */ \"(rsc)/./lib/models/AnalyticsModel.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils_token__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils/token */ \"(rsc)/./lib/utils/token.js\");\n\n\n\n\n\n// Helper function to get user from token\nconst getUserFromToken = (request)=>{\n    try {\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return null;\n        }\n        const token = authHeader.split(\" \")[1];\n        if (!token) {\n            return null;\n        }\n        // Use the verifyToken function from your token utility\n        return (0,_lib_utils_token__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token);\n    } catch (error) {\n        console.error(\"Error getting user from token:\", error);\n        return null;\n    }\n};\n// Track page view\nasync function POST(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const body = await request.json();\n        const { path, contentType, blogId, referrer } = body;\n        // Get user info if logged in\n        const userData = getUserFromToken(request);\n        const userId = userData?.userId || null;\n        // Get IP address and hash it for privacy\n        const ip = request.headers.get(\"x-forwarded-for\") || request.headers.get(\"x-real-ip\") || \"127.0.0.1\";\n        const ipHash = crypto__WEBPACK_IMPORTED_MODULE_3___default().createHash(\"sha256\").update(ip + (process.env.IP_SALT || \"salt\")).digest(\"hex\");\n        // Get user agent\n        const userAgent = request.headers.get(\"user-agent\") || \"\";\n        // Create analytics entry\n        await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n            path,\n            contentType,\n            blogId,\n            userId,\n            ipHash,\n            userAgent,\n            referrer,\n            timestamp: new Date()\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Error tracking analytics:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to track analytics\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Get analytics data\nasync function GET(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        // Verify admin access\n        const userData = getUserFromToken(request);\n        console.log(\"User data from token:\", userData);\n        if (!userData || userData.role !== \"admin\") {\n            console.log(\"Unauthorized access attempt to analytics\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const period = searchParams.get(\"period\") || \"7days\"; // Default to 7 days\n        const blogId = searchParams.get(\"blogId\"); // Optional blog ID filter\n        console.log(\"Fetching analytics for period:\", period);\n        // Calculate date range based on period\n        const endDate = new Date();\n        let startDate = new Date();\n        switch(period){\n            case \"24hours\":\n                startDate.setHours(startDate.getHours() - 24);\n                break;\n            case \"7days\":\n                startDate.setDate(startDate.getDate() - 7);\n                break;\n            case \"30days\":\n                startDate.setDate(startDate.getDate() - 30);\n                break;\n            case \"90days\":\n                startDate.setDate(startDate.getDate() - 90);\n                break;\n            default:\n                startDate.setDate(startDate.getDate() - 7);\n        }\n        // Base query with date range\n        const query = {\n            timestamp: {\n                $gte: startDate,\n                $lte: endDate\n            }\n        };\n        // Add blog ID filter if provided\n        if (blogId) {\n            query.blogId = blogId;\n        }\n        console.log(\"Analytics query:\", JSON.stringify(query));\n        // Get total page views\n        const totalViews = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments(query);\n        console.log(\"Total views:\", totalViews);\n        // Get unique visitors (by IP hash)\n        const uniqueVisitors = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].distinct(\"ipHash\", query).then((ips)=>ips.length);\n        console.log(\"Unique visitors:\", uniqueVisitors);\n        // Get views by content type\n        const viewsByType = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: \"$contentType\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ]);\n        console.log(\"Views by type:\", viewsByType);\n        // Get top pages/posts\n        const topPages = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: \"$path\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    count: -1\n                }\n            },\n            {\n                $limit: 10\n            }\n        ]);\n        console.log(\"Top pages:\", topPages);\n        // Get traffic over time (daily)\n        const trafficOverTime = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: {\n                        $dateToString: {\n                            format: \"%Y-%m-%d\",\n                            date: \"$timestamp\"\n                        }\n                    },\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    \"_id\": 1\n                }\n            }\n        ]);\n        console.log(\"Traffic over time:\", trafficOverTime);\n        // Get referrers\n        const topReferrers = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $match: {\n                    ...query,\n                    referrer: {\n                        $ne: null,\n                        $ne: \"\"\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: \"$referrer\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    count: -1\n                }\n            },\n            {\n                $limit: 10\n            }\n        ]);\n        console.log(\"Top referrers:\", topReferrers);\n        const responseData = {\n            success: true,\n            data: {\n                totalViews,\n                uniqueVisitors,\n                viewsByType,\n                topPages,\n                trafficOverTime,\n                topReferrers\n            }\n        };\n        console.log(\"Analytics response data:\", JSON.stringify(responseData));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(responseData);\n    } catch (error) {\n        console.error(\"Error fetching analytics:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to fetch analytics\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/analytics/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(\"mongodb+srv://subhashanas:<EMAIL>/blog-app\");\n    console.log(\"DB Connected\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/AnalyticsModel.js":
/*!**************************************!*\
  !*** ./lib/models/AnalyticsModel.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst AnalyticsSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    // Page or post being viewed\n    path: {\n        type: String,\n        required: true,\n        index: true\n    },\n    // Type of content (blog, page, etc.)\n    contentType: {\n        type: String,\n        required: true,\n        enum: [\n            \"blog\",\n            \"page\",\n            \"home\",\n            \"other\"\n        ],\n        default: \"other\"\n    },\n    // Associated blog ID if applicable\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"blogs\",\n        index: true\n    },\n    // User ID if logged in\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"users\",\n        index: true\n    },\n    // IP address (hashed for privacy)\n    ipHash: {\n        type: String,\n        index: true\n    },\n    // User agent info\n    userAgent: String,\n    // Referrer URL\n    referrer: String,\n    // Timestamp\n    timestamp: {\n        type: Date,\n        default: Date.now,\n        index: true\n    }\n});\n// Create compound indexes for efficient querying\nAnalyticsSchema.index({\n    path: 1,\n    timestamp: 1\n});\nAnalyticsSchema.index({\n    contentType: 1,\n    timestamp: 1\n});\nAnalyticsSchema.index({\n    blogId: 1,\n    timestamp: 1\n});\n// Check if model exists before creating\nconst AnalyticsModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).analytics || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"analytics\", AnalyticsSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AnalyticsModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/AnalyticsModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/utils/token.js":
/*!****************************!*\
  !*** ./lib/utils/token.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n// Simple token utilities without external dependencies\n\n// Secret key for JWT signing - in production, use environment variables\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here\";\n// Create a token using JWT\nconst createToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n};\n// Verify a token using JWT\nconst verifyToken = (token)=>{\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error(\"Token verification error:\", error.message);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMvdG9rZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHVEQUF1RDtBQUN4QjtBQUUvQix3RUFBd0U7QUFDeEUsTUFBTUMsYUFBYUMsUUFBUUMsR0FBRyxDQUFDRixVQUFVLElBQUk7QUFFN0MsMkJBQTJCO0FBQ3BCLE1BQU1HLGNBQWMsQ0FBQ0M7SUFDMUIsT0FBT0wsd0RBQVEsQ0FBQ0ssU0FBU0osWUFBWTtRQUFFTSxXQUFXO0lBQUs7QUFDekQsRUFBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNQyxjQUFjLENBQUNDO0lBQzFCLElBQUk7UUFDRixPQUFPVCwwREFBVSxDQUFDUyxPQUFPUjtJQUMzQixFQUFFLE9BQU9VLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkEsTUFBTUUsT0FBTztRQUN4RCxPQUFPO0lBQ1Q7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi91dGlscy90b2tlbi5qcz9iOTgxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNpbXBsZSB0b2tlbiB1dGlsaXRpZXMgd2l0aG91dCBleHRlcm5hbCBkZXBlbmRlbmNpZXNcbmltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcblxuLy8gU2VjcmV0IGtleSBmb3IgSldUIHNpZ25pbmcgLSBpbiBwcm9kdWN0aW9uLCB1c2UgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5jb25zdCBKV1RfU0VDUkVUID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAneW91ci1zZWNyZXQta2V5LWhlcmUnO1xuXG4vLyBDcmVhdGUgYSB0b2tlbiB1c2luZyBKV1RcbmV4cG9ydCBjb25zdCBjcmVhdGVUb2tlbiA9IChwYXlsb2FkKSA9PiB7XG4gIHJldHVybiBqd3Quc2lnbihwYXlsb2FkLCBKV1RfU0VDUkVULCB7IGV4cGlyZXNJbjogJzdkJyB9KTtcbn07XG5cbi8vIFZlcmlmeSBhIHRva2VuIHVzaW5nIEpXVFxuZXhwb3J0IGNvbnN0IHZlcmlmeVRva2VuID0gKHRva2VuKSA9PiB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIGp3dC52ZXJpZnkodG9rZW4sIEpXVF9TRUNSRVQpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJUb2tlbiB2ZXJpZmljYXRpb24gZXJyb3I6XCIsIGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59O1xuXG4iXSwibmFtZXMiOlsiand0IiwiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJjcmVhdGVUb2tlbiIsInBheWxvYWQiLCJzaWduIiwiZXhwaXJlc0luIiwidmVyaWZ5VG9rZW4iLCJ0b2tlbiIsInZlcmlmeSIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils/token.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();