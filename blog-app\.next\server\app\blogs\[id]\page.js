/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/blogs/[id]/page";
exports.ids = ["app/blogs/[id]/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblogs%2F%5Bid%5D%2Fpage&page=%2Fblogs%2F%5Bid%5D%2Fpage&appPaths=%2Fblogs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fblogs%2F%5Bid%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblogs%2F%5Bid%5D%2Fpage&page=%2Fblogs%2F%5Bid%5D%2Fpage&appPaths=%2Fblogs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fblogs%2F%5Bid%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'blogs',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/blogs/[id]/page.jsx */ \"(rsc)/./app/blogs/[id]/page.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.jsx */ \"(rsc)/./app/layout.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/blogs/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/blogs/[id]/page\",\n        pathname: \"/blogs/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblogs%2F%5Bid%5D%2Fpage&page=%2Fblogs%2F%5Bid%5D%2Fpage&appPaths=%2Fblogs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fblogs%2F%5Bid%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./Components/CookieConsent.jsx */ \"(ssr)/./Components/CookieConsent.jsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-toastify/dist/react-toastify.esm.mjs */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNDb21wb25lbnRzJTVDQ29va2llQ29uc2VudC5qc3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNEU1lTJTVDRGVza3RvcCU1Q01yLkJsb2clNUNibG9nLWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlNUMlNUNsYXlvdXQuanN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIyT3V0Zml0JTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTJDJTIyd2VpZ2h0JTIyJTNBJTVCJTIyNDAwJTIyJTJDJTIyNTAwJTIyJTJDJTIyNjAwJTIyJTJDJTIyNzAwJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyb3V0Zml0JTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RTWVMlNUNEZXNrdG9wJTVDTXIuQmxvZyU1Q2Jsb2ctYXBwJTVDbm9kZV9tb2R1bGVzJTVDcmVhY3QtdG9hc3RpZnklNUNkaXN0JTVDcmVhY3QtdG9hc3RpZnkuZXNtLm1qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RTWVMlNUNEZXNrdG9wJTVDTXIuQmxvZyU1Q2Jsb2ctYXBwJTVDbm9kZV9tb2R1bGVzJTVDcmVhY3QtdG9hc3RpZnklNUNkaXN0JTVDUmVhY3RUb2FzdGlmeS5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUErRztBQUMvRyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvP2VhYzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEU1lTXFxcXERlc2t0b3BcXFxcTXIuQmxvZ1xcXFxibG9nLWFwcFxcXFxDb21wb25lbnRzXFxcXENvb2tpZUNvbnNlbnQuanN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEU1lTXFxcXERlc2t0b3BcXFxcTXIuQmxvZ1xcXFxibG9nLWFwcFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtdG9hc3RpZnlcXFxcZGlzdFxcXFxyZWFjdC10b2FzdGlmeS5lc20ubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cblogs%5C%5Bid%5D%5Cpage.jsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cblogs%5C%5Bid%5D%5Cpage.jsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/blogs/[id]/page.jsx */ \"(ssr)/./app/blogs/[id]/page.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNhcHAlNUNibG9ncyU1QyU1QmlkJTVEJTVDcGFnZS5qc3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8/OWViZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXGFwcFxcXFxibG9nc1xcXFxbaWRdXFxcXHBhZ2UuanN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cblogs%5C%5Bid%5D%5Cpage.jsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./Assets/assets.js":
/*!**************************!*\
  !*** ./Assets/assets.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assets: () => (/* binding */ assets),\n/* harmony export */   blog_data: () => (/* binding */ blog_data)\n/* harmony export */ });\n/* harmony import */ var _blog_pic_1_png__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blog_pic_1.png */ \"(ssr)/./Assets/blog_pic_1.png\");\n/* harmony import */ var _blog_pic_2_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./blog_pic_2.png */ \"(ssr)/./Assets/blog_pic_2.png\");\n/* harmony import */ var _blog_pic_3_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./blog_pic_3.png */ \"(ssr)/./Assets/blog_pic_3.png\");\n/* harmony import */ var _blog_pic_4_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog_pic_4.png */ \"(ssr)/./Assets/blog_pic_4.png\");\n/* harmony import */ var _blog_pic_5_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./blog_pic_5.png */ \"(ssr)/./Assets/blog_pic_5.png\");\n/* harmony import */ var _blog_pic_6_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./blog_pic_6.png */ \"(ssr)/./Assets/blog_pic_6.png\");\n/* harmony import */ var _blog_pic_7_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./blog_pic_7.png */ \"(ssr)/./Assets/blog_pic_7.png\");\n/* harmony import */ var _blog_pic_8_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./blog_pic_8.png */ \"(ssr)/./Assets/blog_pic_8.png\");\n/* harmony import */ var _blog_pic_9_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./blog_pic_9.png */ \"(ssr)/./Assets/blog_pic_9.png\");\n/* harmony import */ var _blog_pic_10_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./blog_pic_10.png */ \"(ssr)/./Assets/blog_pic_10.png\");\n/* harmony import */ var _blog_pic_11_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./blog_pic_11.png */ \"(ssr)/./Assets/blog_pic_11.png\");\n/* harmony import */ var _blog_pic_12_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./blog_pic_12.png */ \"(ssr)/./Assets/blog_pic_12.png\");\n/* harmony import */ var _blog_pic_13_png__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./blog_pic_13.png */ \"(ssr)/./Assets/blog_pic_13.png\");\n/* harmony import */ var _blog_pic_14_png__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./blog_pic_14.png */ \"(ssr)/./Assets/blog_pic_14.png\");\n/* harmony import */ var _blog_pic_15_png__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./blog_pic_15.png */ \"(ssr)/./Assets/blog_pic_15.png\");\n/* harmony import */ var _blog_pic_16_png__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./blog_pic_16.png */ \"(ssr)/./Assets/blog_pic_16.png\");\n/* harmony import */ var _facebook_icon_png__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./facebook_icon.png */ \"(ssr)/./Assets/facebook_icon.png\");\n/* harmony import */ var _googleplus_icon_png__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./googleplus_icon.png */ \"(ssr)/./Assets/googleplus_icon.png\");\n/* harmony import */ var _twitter_icon_png__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./twitter_icon.png */ \"(ssr)/./Assets/twitter_icon.png\");\n/* harmony import */ var _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./profile_icon.png */ \"(ssr)/./Assets/profile_icon.png\");\n/* harmony import */ var _logo_png__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./logo.png */ \"(ssr)/./Assets/logo.png\");\n/* harmony import */ var _arrow_png__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./arrow.png */ \"(ssr)/./Assets/arrow.png\");\n/* harmony import */ var _logo_light_png__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./logo_light.png */ \"(ssr)/./Assets/logo_light.png\");\n/* harmony import */ var _blog_icon_png__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./blog_icon.png */ \"(ssr)/./Assets/blog_icon.png\");\n/* harmony import */ var _add_icon_png__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./add_icon.png */ \"(ssr)/./Assets/add_icon.png\");\n/* harmony import */ var _email_icon_png__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./email_icon.png */ \"(ssr)/./Assets/email_icon.png\");\n/* harmony import */ var _upload_area_png__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./upload_area.png */ \"(ssr)/./Assets/upload_area.png\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst assets = {\n    facebook_icon: _facebook_icon_png__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    googleplus_icon: _googleplus_icon_png__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    twitter_icon: _twitter_icon_png__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    profile_icon: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    logo: _logo_png__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    arrow: _arrow_png__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n    logo_light: _logo_light_png__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n    blog_icon: _blog_icon_png__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n    add_icon: _add_icon_png__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n    email_icon: _email_icon_png__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n    upload_area: _upload_area_png__WEBPACK_IMPORTED_MODULE_26__[\"default\"]\n};\nconst blog_data = [\n    {\n        id: 1,\n        title: \"A detailed step by step guide to manage your lifestyle\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_1_png__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 2,\n        title: \"How to create an effective startup roadmap or ideas\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_2_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 3,\n        title: \"Learning new technology to boost your career in software\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_3_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 4,\n        title: \"Tips for getting the most out of apps and software\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_4_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 5,\n        title: \"Enhancing your skills and capturing memorable moments\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_5_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 6,\n        title: \"Maximizing returns by minimizing resources in your startup\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_6_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 7,\n        title: \"Technology for Career advancement in development\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_7_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 8,\n        title: \"A comprehensive roadmap for effective lifestyle management\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_8_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 9,\n        title: \"Achieving maximum returns with minimal resources\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_9_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 10,\n        title: \"Beyond the Ordinary: Crafting Your Exceptional Lifestyle\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_10_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 11,\n        title: \"Unveiling the Secrets of Successful Startups in Technolgy\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_11_png__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 12,\n        title: \"How to design an online Learning Platform today\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_12_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 13,\n        title: \"Tomorrow's Algorithms: Shaping the Landscape of Future AI\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_13_png__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 14,\n        title: \"Balance & Bliss: Navigating Life's Journey with Style\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_14_png__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 15,\n        title: \"Exploring the Evolution of social networking in the Future\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_15_png__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 16,\n        title: \"Shaping the Future of statup ecosystem in the world\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_16_png__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYXNzZXRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0c7QUFDSTtBQUNOO0FBQ0E7QUFDaEI7QUFDRTtBQUNVO0FBQ0Y7QUFDRjtBQUNJO0FBQ0U7QUFFcEMsTUFBTTJCLFNBQVM7SUFDbEJYLGFBQWFBLDZEQUFBQTtJQUNiQyxlQUFlQSwrREFBQUE7SUFDZkMsWUFBWUEsNERBQUFBO0lBQ1pDLFlBQVlBLDREQUFBQTtJQUNaQyxJQUFJQSxvREFBQUE7SUFDSkMsS0FBS0EscURBQUFBO0lBQ0xDLFVBQVVBLDBEQUFBQTtJQUNWQyxTQUFTQSx5REFBQUE7SUFDVEMsUUFBUUEsd0RBQUFBO0lBQ1JDLFVBQVVBLDBEQUFBQTtJQUNWQyxXQUFXQSwyREFBQUE7QUFDWCxFQUFDO0FBRU0sTUFBTUUsWUFBWTtJQUFDO1FBQ3RCQyxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNaEMsdURBQVVBO1FBQ2hCaUMsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU0vQix1REFBVUE7UUFDaEJnQyxNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTTlCLHVEQUFVQTtRQUNoQitCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNN0IsdURBQVVBO1FBQ2hCOEIsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU01Qix1REFBVUE7UUFDaEI2QixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTTNCLHVEQUFVQTtRQUNoQjRCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNMUIsdURBQVVBO1FBQ2hCMkIsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU16Qix1REFBVUE7UUFDaEIwQixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTXhCLHVEQUFVQTtRQUNoQnlCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNdkIsd0RBQVdBO1FBQ2pCd0IsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU10Qix5REFBV0E7UUFDakJ1QixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTXJCLHlEQUFXQTtRQUNqQnNCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNcEIseURBQVdBO1FBQ2pCcUIsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU1uQix5REFBV0E7UUFDakJvQixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTWxCLHlEQUFXQTtRQUNqQm1CLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNakIseURBQVdBO1FBQ2pCa0IsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtDQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9hc3NldHMuanM/YzUxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYmxvZ19waWNfMSBmcm9tICcuL2Jsb2dfcGljXzEucG5nJztcclxuaW1wb3J0IGJsb2dfcGljXzIgZnJvbSAnLi9ibG9nX3BpY18yLnBuZyc7XHJcbmltcG9ydCBibG9nX3BpY18zIGZyb20gJy4vYmxvZ19waWNfMy5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfNCBmcm9tICcuL2Jsb2dfcGljXzQucG5nJztcclxuaW1wb3J0IGJsb2dfcGljXzUgZnJvbSAnLi9ibG9nX3BpY181LnBuZyc7XHJcbmltcG9ydCBibG9nX3BpY182IGZyb20gJy4vYmxvZ19waWNfNi5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfNyBmcm9tICcuL2Jsb2dfcGljXzcucG5nJztcclxuaW1wb3J0IGJsb2dfcGljXzggZnJvbSAnLi9ibG9nX3BpY184LnBuZyc7XHJcbmltcG9ydCBibG9nX3BpY185IGZyb20gJy4vYmxvZ19waWNfOS5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTAgZnJvbSAnLi9ibG9nX3BpY18xMC5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTEgZnJvbSAnLi9ibG9nX3BpY18xMS5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTIgZnJvbSAnLi9ibG9nX3BpY18xMi5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTMgZnJvbSAnLi9ibG9nX3BpY18xMy5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTQgZnJvbSAnLi9ibG9nX3BpY18xNC5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTUgZnJvbSAnLi9ibG9nX3BpY18xNS5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTYgZnJvbSAnLi9ibG9nX3BpY18xNi5wbmcnO1xyXG5pbXBvcnQgZmFjZWJvb2tfaWNvbiBmcm9tICcuL2ZhY2Vib29rX2ljb24ucG5nJ1xyXG5pbXBvcnQgZ29vZ2xlcGx1c19pY29uIGZyb20gJy4vZ29vZ2xlcGx1c19pY29uLnBuZydcclxuaW1wb3J0IHR3aXR0ZXJfaWNvbiBmcm9tICcuL3R3aXR0ZXJfaWNvbi5wbmcnXHJcbmltcG9ydCBwcm9maWxlX2ljb24gZnJvbSAnLi9wcm9maWxlX2ljb24ucG5nJ1xyXG5pbXBvcnQgbG9nbyBmcm9tICcuL2xvZ28ucG5nJ1xyXG5pbXBvcnQgYXJyb3cgZnJvbSAnLi9hcnJvdy5wbmcnXHJcbmltcG9ydCBsb2dvX2xpZ2h0IGZyb20gJy4vbG9nb19saWdodC5wbmcnXHJcbmltcG9ydCBibG9nX2ljb24gZnJvbSAnLi9ibG9nX2ljb24ucG5nJ1xyXG5pbXBvcnQgYWRkX2ljb24gZnJvbSAnLi9hZGRfaWNvbi5wbmcnXHJcbmltcG9ydCBlbWFpbF9pY29uIGZyb20gJy4vZW1haWxfaWNvbi5wbmcnXHJcbmltcG9ydCB1cGxvYWRfYXJlYSBmcm9tICcuL3VwbG9hZF9hcmVhLnBuZydcclxuXHJcbmV4cG9ydCBjb25zdCBhc3NldHMgPSB7XHJcbiAgICBmYWNlYm9va19pY29uLFxyXG4gICAgZ29vZ2xlcGx1c19pY29uLFxyXG4gICAgdHdpdHRlcl9pY29uLFxyXG4gICAgcHJvZmlsZV9pY29uLFxyXG4gICAgbG9nbyxcclxuICAgIGFycm93LFxyXG4gICAgbG9nb19saWdodCxcclxuICAgIGJsb2dfaWNvbixcclxuICAgIGFkZF9pY29uLFxyXG4gICAgZW1haWxfaWNvbixcclxuICAgIHVwbG9hZF9hcmVhXHJcbiAgICB9XHJcblxyXG4gICAgZXhwb3J0IGNvbnN0IGJsb2dfZGF0YSA9IFt7XHJcbiAgICAgICAgaWQ6MSxcclxuICAgICAgICB0aXRsZTpcIkEgZGV0YWlsZWQgc3RlcCBieSBzdGVwIGd1aWRlIHRvIG1hbmFnZSB5b3VyIGxpZmVzdHlsZVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY18xLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIkxpZmVzdHlsZVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjIsXHJcbiAgICAgICAgdGl0bGU6XCJIb3cgdG8gY3JlYXRlIGFuIGVmZmVjdGl2ZSBzdGFydHVwIHJvYWRtYXAgb3IgaWRlYXNcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjpcIkxvcmVtIElwc3VtIGlzIHNpbXBseSBkdW1teSB0ZXh0IG9mIHRoZSBwcmludGluZyBhbmQgdHlwZXNldHRpbmcgaW5kdXN0cnkuIExvcmVtIElwc3VtIGhhcyBiZWVuIHRoZS4uXCIsXHJcbiAgICAgICAgaW1hZ2U6YmxvZ19waWNfMixcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJTdGFydHVwXCIsXHJcbiAgICAgICAgYXV0aG9yOlwiQWxleCBCZW5uZXR0XCIsXHJcbiAgICAgICAgYXV0aG9yX2ltZzpwcm9maWxlX2ljb25cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgaWQ6MyxcclxuICAgICAgICB0aXRsZTpcIkxlYXJuaW5nIG5ldyB0ZWNobm9sb2d5IHRvIGJvb3N0IHlvdXIgY2FyZWVyIGluIHNvZnR3YXJlXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzMsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiVGVjaG5vbG9neVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjQsXHJcbiAgICAgICAgdGl0bGU6XCJUaXBzIGZvciBnZXR0aW5nIHRoZSBtb3N0IG91dCBvZiBhcHBzIGFuZCBzb2Z0d2FyZVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY180LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlRlY2hub2xvZ3lcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDo1LFxyXG4gICAgICAgIHRpdGxlOlwiRW5oYW5jaW5nIHlvdXIgc2tpbGxzIGFuZCBjYXB0dXJpbmcgbWVtb3JhYmxlIG1vbWVudHNcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjpcIkxvcmVtIElwc3VtIGlzIHNpbXBseSBkdW1teSB0ZXh0IG9mIHRoZSBwcmludGluZyBhbmQgdHlwZXNldHRpbmcgaW5kdXN0cnkuIExvcmVtIElwc3VtIGhhcyBiZWVuIHRoZS4uXCIsXHJcbiAgICAgICAgaW1hZ2U6YmxvZ19waWNfNSxcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJMaWZlc3R5bGVcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDo2LFxyXG4gICAgICAgIHRpdGxlOlwiTWF4aW1pemluZyByZXR1cm5zIGJ5IG1pbmltaXppbmcgcmVzb3VyY2VzIGluIHlvdXIgc3RhcnR1cFwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY182LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDo3LFxyXG4gICAgICAgIHRpdGxlOlwiVGVjaG5vbG9neSBmb3IgQ2FyZWVyIGFkdmFuY2VtZW50IGluIGRldmVsb3BtZW50XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzcsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiVGVjaG5vbG9neVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjgsXHJcbiAgICAgICAgdGl0bGU6XCJBIGNvbXByZWhlbnNpdmUgcm9hZG1hcCBmb3IgZWZmZWN0aXZlIGxpZmVzdHlsZSBtYW5hZ2VtZW50XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzgsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiTGlmZXN0eWxlXCIsXHJcbiAgICAgICAgYXV0aG9yOlwiQWxleCBCZW5uZXR0XCIsXHJcbiAgICAgICAgYXV0aG9yX2ltZzpwcm9maWxlX2ljb25cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgaWQ6OSxcclxuICAgICAgICB0aXRsZTpcIkFjaGlldmluZyBtYXhpbXVtIHJldHVybnMgd2l0aCBtaW5pbWFsIHJlc291cmNlc1wiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY185LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxMCxcclxuICAgICAgICB0aXRsZTpcIkJleW9uZCB0aGUgT3JkaW5hcnk6IENyYWZ0aW5nIFlvdXIgRXhjZXB0aW9uYWwgTGlmZXN0eWxlXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzEwLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIkxpZmVzdHlsZVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjExLFxyXG4gICAgICAgIHRpdGxlOlwiVW52ZWlsaW5nIHRoZSBTZWNyZXRzIG9mIFN1Y2Nlc3NmdWwgU3RhcnR1cHMgaW4gVGVjaG5vbGd5XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzExLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxMixcclxuICAgICAgICB0aXRsZTpcIkhvdyB0byBkZXNpZ24gYW4gb25saW5lIExlYXJuaW5nIFBsYXRmb3JtIHRvZGF5XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzEyLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlRlY2hub2xvZ3lcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxMyxcclxuICAgICAgICB0aXRsZTpcIlRvbW9ycm93J3MgQWxnb3JpdGhtczogU2hhcGluZyB0aGUgTGFuZHNjYXBlIG9mIEZ1dHVyZSBBSVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY18xMyxcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJTdGFydHVwXCIsXHJcbiAgICAgICAgYXV0aG9yOlwiQWxleCBCZW5uZXR0XCIsXHJcbiAgICAgICAgYXV0aG9yX2ltZzpwcm9maWxlX2ljb25cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgaWQ6MTQsXHJcbiAgICAgICAgdGl0bGU6XCJCYWxhbmNlICYgQmxpc3M6IE5hdmlnYXRpbmcgTGlmZSdzIEpvdXJuZXkgd2l0aCBTdHlsZVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY18xNCxcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJMaWZlc3R5bGVcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxNSxcclxuICAgICAgICB0aXRsZTpcIkV4cGxvcmluZyB0aGUgRXZvbHV0aW9uIG9mIHNvY2lhbCBuZXR3b3JraW5nIGluIHRoZSBGdXR1cmVcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjpcIkxvcmVtIElwc3VtIGlzIHNpbXBseSBkdW1teSB0ZXh0IG9mIHRoZSBwcmludGluZyBhbmQgdHlwZXNldHRpbmcgaW5kdXN0cnkuIExvcmVtIElwc3VtIGhhcyBiZWVuIHRoZS4uXCIsXHJcbiAgICAgICAgaW1hZ2U6YmxvZ19waWNfMTUsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiVGVjaG5vbG9neVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjE2LFxyXG4gICAgICAgIHRpdGxlOlwiU2hhcGluZyB0aGUgRnV0dXJlIG9mIHN0YXR1cCBlY29zeXN0ZW0gaW4gdGhlIHdvcmxkXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzE2LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuXSJdLCJuYW1lcyI6WyJibG9nX3BpY18xIiwiYmxvZ19waWNfMiIsImJsb2dfcGljXzMiLCJibG9nX3BpY180IiwiYmxvZ19waWNfNSIsImJsb2dfcGljXzYiLCJibG9nX3BpY183IiwiYmxvZ19waWNfOCIsImJsb2dfcGljXzkiLCJibG9nX3BpY18xMCIsImJsb2dfcGljXzExIiwiYmxvZ19waWNfMTIiLCJibG9nX3BpY18xMyIsImJsb2dfcGljXzE0IiwiYmxvZ19waWNfMTUiLCJibG9nX3BpY18xNiIsImZhY2Vib29rX2ljb24iLCJnb29nbGVwbHVzX2ljb24iLCJ0d2l0dGVyX2ljb24iLCJwcm9maWxlX2ljb24iLCJsb2dvIiwiYXJyb3ciLCJsb2dvX2xpZ2h0IiwiYmxvZ19pY29uIiwiYWRkX2ljb24iLCJlbWFpbF9pY29uIiwidXBsb2FkX2FyZWEiLCJhc3NldHMiLCJibG9nX2RhdGEiLCJpZCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJpbWFnZSIsImRhdGUiLCJEYXRlIiwibm93IiwiY2F0ZWdvcnkiLCJhdXRob3IiLCJhdXRob3JfaW1nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/assets.js\n");

/***/ }),

/***/ "(ssr)/./Components/CookieConsent.jsx":
/*!**************************************!*\
  !*** ./Components/CookieConsent.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/cookieUtils */ \"(ssr)/./utils/cookieUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CookieConsent = ()=>{\n    const [showConsent, setShowConsent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user has already made a choice\n        const consentCookie = (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"cookie-consent\");\n        if (!consentCookie) {\n            // Set a timeout to show the consent popup after 10 seconds\n            const timer = setTimeout(()=>{\n                setShowConsent(true);\n            }, 10000) // 10 seconds\n            ;\n            return ()=>clearTimeout(timer);\n        } else if (consentCookie === \"accepted\") {\n            // If user has accepted cookies, set the necessary cookies\n            (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setAnalyticsCookies)();\n            (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setFunctionalCookies)();\n        }\n    }, []);\n    const acceptCookies = ()=>{\n        // Set cookie with 1-year expiry\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setCookie)(\"cookie-consent\", \"accepted\", {\n            expires: 365\n        });\n        // Set other cookies as needed\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setAnalyticsCookies)();\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setFunctionalCookies)();\n        setShowConsent(false);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Cookie preferences saved\");\n    };\n    const declineCookies = ()=>{\n        // Set cookie to remember user declined\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setCookie)(\"cookie-consent\", \"declined\", {\n            expires: 365\n        });\n        setShowConsent(false);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Cookies declined. Some features may be limited.\");\n    };\n    if (!showConsent) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-0 left-0 right-0 bg-white shadow-lg z-50 border-t border-gray-200 p-4 md:p-6 animate-fade-in\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row md:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"We use cookies\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm md:text-base\",\n                                children: [\n                                    'We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking \"Accept All\", you consent to our use of cookies.',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/privacy-policy\",\n                                        className: \"text-blue-600 hover:underline ml-1\",\n                                        children: \"Read our Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: declineCookies,\n                                className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 text-sm md:text-base\",\n                                children: \"Decline\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: acceptCookies,\n                                className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 text-sm md:text-base\",\n                                children: \"Accept All\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieConsent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/CookieConsent.jsx\n");

/***/ }),

/***/ "(ssr)/./Components/Footer.jsx":
/*!*******************************!*\
  !*** ./Components/Footer.jsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(ssr)/./Assets/assets.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst Footer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-around flex-col gap-2 sm:gap-0 sm:flex-row bg-black py-5 items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.logo_light,\n                alt: \"Mr.Blogger\",\n                width: 120\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center gap-4 mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/about\",\n                                className: \"text-sm text-white hover:underline\",\n                                children: \"About Us\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                                lineNumber: 12,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/contact\",\n                                className: \"text-sm text-white hover:underline\",\n                                children: \"Contact Us\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-white\",\n                        children: \"All right reserved. Copyright @Mr.Blogger\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.facebook_icon,\n                        alt: \"\",\n                        width: 40\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.twitter_icon,\n                        alt: \"\",\n                        width: 40\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.googleplus_icon,\n                        alt: \"\",\n                        width: 40\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Footer.jsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/Footer.jsx\n");

/***/ }),

/***/ "(ssr)/./Components/Icons.jsx":
/*!******************************!*\
  !*** ./Components/Icons.jsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowIcon: () => (/* binding */ ArrowIcon),\n/* harmony export */   FavoriteIcon: () => (/* binding */ FavoriteIcon),\n/* harmony export */   LikeIcon: () => (/* binding */ LikeIcon),\n/* harmony export */   ShareIcon: () => (/* binding */ ShareIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst LikeIcon = ({ filled, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: filled ? \"currentColor\" : \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n            lineNumber: 16,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n        lineNumber: 4,\n        columnNumber: 3\n    }, undefined);\nconst FavoriteIcon = ({ filled, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: filled ? \"currentColor\" : \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n            points: \"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n            lineNumber: 33,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined);\nconst ArrowIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"5\",\n                y1: \"12\",\n                x2: \"19\",\n                y2: \"12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n                lineNumber: 50,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"12 5 19 12 12 19\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n                lineNumber: 51,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n        lineNumber: 38,\n        columnNumber: 3\n    }, undefined);\nconst ShareIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"18\",\n                cy: \"5\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n                lineNumber: 68,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"6\",\n                cy: \"12\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n                lineNumber: 69,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"18\",\n                cy: \"19\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n                lineNumber: 70,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"8.59\",\n                y1: \"13.51\",\n                x2: \"15.42\",\n                y2: \"17.49\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n                lineNumber: 71,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"15.41\",\n                y1: \"6.51\",\n                x2: \"8.59\",\n                y2: \"10.49\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n                lineNumber: 72,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Icons.jsx\",\n        lineNumber: 56,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/Icons.jsx\n");

/***/ }),

/***/ "(ssr)/./Components/TrendingBlogs.jsx":
/*!**************************************!*\
  !*** ./Components/TrendingBlogs.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Icons */ \"(ssr)/./Components/Icons.jsx\");\n\n\n\n\n\nconst TrendingBlogs = ({ blogs, currentBlogId })=>{\n    // Filter out the current blog and limit to 12 blogs\n    const filteredBlogs = blogs.filter((blog)=>blog._id !== currentBlogId).slice(0, 12);\n    // State for carousel\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Number of visible items and peek amount based on screen size\n    const [visibleItems, setVisibleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(4);\n    const [peekAmount, setPeekAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(80); // pixels to show of the next item\n    // Update visible items on window resize\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (window.innerWidth < 640) {\n                setVisibleItems(1);\n                setPeekAmount(40);\n            } else if (window.innerWidth < 768) {\n                setVisibleItems(2);\n                setPeekAmount(60);\n            } else if (window.innerWidth < 1024) {\n                setVisibleItems(3);\n                setPeekAmount(70);\n            } else {\n                setVisibleItems(4);\n                setPeekAmount(80);\n            }\n        };\n        // Initial call\n        handleResize();\n        // Add event listener\n        window.addEventListener(\"resize\", handleResize);\n        // Cleanup\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    // Auto-slide functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (filteredBlogs.length <= visibleItems) return;\n        const startTimer = ()=>{\n            timerRef.current = setTimeout(()=>{\n                if (!isAnimating) {\n                    nextSlide();\n                }\n            }, 2000); // Changed from 1000 to 2000 ms (2 seconds)\n        };\n        startTimer();\n        return ()=>{\n            if (timerRef.current) clearTimeout(timerRef.current);\n        };\n    }, [\n        currentIndex,\n        filteredBlogs.length,\n        visibleItems,\n        isAnimating\n    ]);\n    // Calculate item width based on visible items and peek amount\n    const calculateItemWidth = ()=>{\n        if (!carouselRef.current) return `${100 / filteredBlogs.length}%`;\n        const containerWidth = carouselRef.current.offsetWidth;\n        const itemWidth = (containerWidth - peekAmount) / visibleItems;\n        return `${itemWidth}px`;\n    };\n    // Handle manual navigation\n    const nextSlide = ()=>{\n        if (isAnimating || filteredBlogs.length <= visibleItems) return;\n        setIsAnimating(true);\n        setCurrentIndex((prevIndex)=>prevIndex === filteredBlogs.length - visibleItems ? 0 : prevIndex + 1);\n        // Reduce animation time to match faster slide interval\n        setTimeout(()=>{\n            setIsAnimating(false);\n        }, 300); // Changed from 500 to 300 ms\n    };\n    const prevSlide = ()=>{\n        if (isAnimating || filteredBlogs.length <= visibleItems) return;\n        setIsAnimating(true);\n        setCurrentIndex((prevIndex)=>prevIndex === 0 ? filteredBlogs.length - visibleItems : prevIndex - 1);\n        // Reduce animation time to match faster slide interval\n        setTimeout(()=>{\n            setIsAnimating(false);\n        }, 300); // Changed from 500 to 300 ms\n    };\n    // Reset timer when manually navigating\n    const handleNavigation = (callback)=>{\n        if (timerRef.current) clearTimeout(timerRef.current);\n        callback();\n        timerRef.current = setTimeout(()=>{\n            nextSlide();\n        }, 2000); // Changed from 1000 to 2000 ms (2 seconds)\n    };\n    if (filteredBlogs.length === 0) {\n        return null;\n    }\n    // Calculate the slide offset with peek amount\n    const slideOffset = ()=>{\n        if (!carouselRef.current) return `${currentIndex * (100 / visibleItems)}%`;\n        const containerWidth = carouselRef.current.offsetWidth;\n        const itemWidth = (containerWidth - peekAmount) / visibleItems;\n        return `${currentIndex * itemWidth}px`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-12 border-t border-gray-200 pt-10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl sm:text-2xl font-bold mb-6 text-center\",\n                children: \"Trending Articles\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative px-4\",\n                children: [\n                    filteredBlogs.length > visibleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleNavigation(prevSlide),\n                                className: \"absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md\",\n                                \"aria-label\": \"Previous\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 19l-7-7 7-7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleNavigation(nextSlide),\n                                className: \"absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md\",\n                                \"aria-label\": \"Next\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 5l7 7-7 7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden\",\n                        ref: carouselRef,\n                        style: {\n                            paddingRight: `${peekAmount}px`\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex transition-transform duration-300 ease-in-out\" // Changed from duration-500 to duration-300\n                            ,\n                            style: {\n                                transform: `translateX(-${slideOffset()})`\n                            },\n                            children: filteredBlogs.map((blog)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pr-4 flex-shrink-0\",\n                                    style: {\n                                        width: calculateItemWidth()\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-gray-200 transition-all hover:shadow-md h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: `/blogs/${blog._id}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-36 border-b border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        src: blog.image,\n                                                        alt: blog.title,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"px-1.5 py-0.5 mb-1.5 inline-block bg-gray-100 text-gray-800 text-xs\",\n                                                        children: blog.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium mb-1.5 line-clamp-2\",\n                                                        children: blog.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: `/blogs/${blog._id}`,\n                                                        className: \"inline-flex items-center text-xs font-semibold text-gray-700\",\n                                                        children: [\n                                                            \"Read more \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_4__.ArrowIcon, {\n                                                                className: \"ml-1 w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, blog._id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined),\n                    filteredBlogs.length > visibleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-4\",\n                        children: Array.from({\n                            length: filteredBlogs.length - visibleItems + 1\n                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleNavigation(()=>{\n                                        setIsAnimating(true);\n                                        setCurrentIndex(index);\n                                        setTimeout(()=>setIsAnimating(false), 500);\n                                    }),\n                                className: `h-2 w-2 mx-1 rounded-full ${currentIndex === index ? \"bg-gray-800\" : \"bg-gray-300\"}`,\n                                \"aria-label\": `Go to slide ${index + 1}`\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                                lineNumber: 208,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\TrendingBlogs.jsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TrendingBlogs);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/TrendingBlogs.jsx\n");

/***/ }),

/***/ "(ssr)/./app/blogs/[id]/page.jsx":
/*!*********************************!*\
  !*** ./app/blogs/[id]/page.jsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPost)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(ssr)/./Assets/assets.js\");\n/* harmony import */ var _Components_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/Components/Footer */ \"(ssr)/./Components/Footer.jsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(ssr)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _utils_analyticsUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/analyticsUtils */ \"(ssr)/./utils/analyticsUtils.js\");\n/* harmony import */ var _Components_TrendingBlogs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/Components/TrendingBlogs */ \"(ssr)/./Components/TrendingBlogs.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nfunction BlogPost({ params }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [showLoginModal, setShowLoginModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [loginData, setLoginData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [isRegistering, setIsRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [registerData, setRegisterData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        role: \"user\" // Default role, not selectable by user\n    });\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showAccountDropdown, setShowAccountDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [userProfilePicture, setUserProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"/default_profile.png\");\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLogoutConfirm, setShowLogoutConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [favoriteLoading, setFavoriteLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isLiked, setIsLiked] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [likeLoading, setLikeLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [likesCount, setLikesCount] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(0);\n    const [trendingBlogs, setTrendingBlogs] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const handleLoginChange = (e)=>{\n        setLoginData({\n            ...loginData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleRegisterChange = (e)=>{\n        setRegisterData({\n            ...registerData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleLoginSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post(\"/api/auth\", {\n                email: loginData.email,\n                password: loginData.password\n            });\n            if (response.data.success) {\n                // Store auth data in localStorage\n                localStorage.setItem(\"authToken\", response.data.token || \"dummy-token\");\n                localStorage.setItem(\"userRole\", response.data.user.role);\n                localStorage.setItem(\"userId\", response.data.user.id); // Add this line\n                // Handle remember me functionality\n                if (rememberMe) {\n                    localStorage.setItem(\"rememberedEmail\", loginData.email);\n                    localStorage.setItem(\"rememberedPassword\", loginData.password);\n                    localStorage.setItem(\"rememberMe\", \"true\");\n                } else {\n                    localStorage.removeItem(\"rememberedEmail\");\n                    localStorage.removeItem(\"rememberedPassword\");\n                    localStorage.removeItem(\"rememberMe\");\n                }\n                // Check if user is admin\n                if (response.data.user.role === \"admin\") {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    window.location.href = \"/admin\";\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    // Redirect regular users to homepage\n                    window.location.href = \"/\";\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Invalid credentials\");\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.response?.data?.message || \"Login failed\");\n        }\n    };\n    const handleRegisterSubmit = async (e)=>{\n        e.preventDefault();\n        // Validate passwords match\n        if (registerData.password !== registerData.confirmPassword) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Passwords do not match\");\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post(\"/api/register\", {\n                email: registerData.email,\n                password: registerData.password,\n                role: registerData.role // Always \"user\" for public registration\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Registration successful! Please login.\");\n                // Switch back to login form\n                setIsRegistering(false);\n                // Pre-fill email in login form\n                setLoginData({\n                    ...loginData,\n                    email: registerData.email\n                });\n                // Reset register form\n                setRegisterData({\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\",\n                    role: \"user\"\n                });\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(response.data.message || \"Registration failed\");\n            }\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.response?.data?.message || \"Registration failed\");\n        }\n    };\n    const toggleForm = ()=>{\n        setIsRegistering(!isRegistering);\n    };\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    const fetchBlogData = async ()=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get(\"/api/blog\", {\n            params: {\n                id: params.id\n            }\n        });\n        setData(response.data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        fetchBlogData();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        // Check for remembered credentials\n        const rememberedEmail = localStorage.getItem(\"rememberedEmail\");\n        const rememberedPassword = localStorage.getItem(\"rememberedPassword\");\n        const wasRemembered = localStorage.getItem(\"rememberMe\") === \"true\";\n        if (rememberedEmail && rememberedPassword && wasRemembered) {\n            setLoginData({\n                email: rememberedEmail,\n                password: rememberedPassword\n            });\n            setRememberMe(true);\n        }\n        const authToken = localStorage.getItem(\"authToken\");\n        const storedUserRole = localStorage.getItem(\"userRole\");\n        const storedProfilePicture = localStorage.getItem(\"userProfilePicture\");\n        const storedUserName = localStorage.getItem(\"userName\");\n        if (authToken) {\n            setIsLoggedIn(true);\n            setUserRole(storedUserRole || \"user\");\n            if (storedProfilePicture) {\n                setUserProfilePicture(storedProfilePicture);\n            }\n            if (storedUserName) {\n                setUserName(storedUserName);\n            }\n        }\n    }, []);\n    const handleLogoutClick = ()=>{\n        setShowLogoutConfirm(true);\n        setShowAccountDropdown(false);\n    };\n    const handleLogoutConfirm = ()=>{\n        // Clear auth data from localStorage\n        localStorage.removeItem(\"authToken\");\n        localStorage.removeItem(\"userRole\");\n        // Update state\n        setIsLoggedIn(false);\n        setUserRole(\"\");\n        setShowLogoutConfirm(false);\n        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Logged out successfully\");\n    };\n    const handleLogoutCancel = ()=>{\n        setShowLogoutConfirm(false);\n    };\n    const toggleAccountDropdown = ()=>{\n        setShowAccountDropdown(!showAccountDropdown);\n    };\n    const checkFavoriteStatus = async (token)=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get(`/api/favorites?blogId=${params.id}`, {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            if (response.data.success) {\n                setIsFavorite(response.data.isFavorite);\n            }\n        } catch (error) {\n            console.error(\"Error checking favorite status:\", error);\n        }\n    };\n    const toggleFavorite = async ()=>{\n        if (!isLoggedIn) {\n            // Show login modal or redirect to login\n            setShowLoginModal(true);\n            return;\n        }\n        try {\n            setFavoriteLoading(true);\n            const token = localStorage.getItem(\"authToken\");\n            console.log(\"Using token:\", token);\n            if (isFavorite) {\n                // Remove from favorites\n                const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].delete(`/api/favorites?blogId=${params.id}`, {\n                    headers: {\n                        Authorization: `Bearer ${token}`\n                    }\n                });\n                if (response.data.success) {\n                    setIsFavorite(false);\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Removed from favorites\");\n                }\n            } else {\n                // Add to favorites\n                const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post(\"/api/favorites\", {\n                    blogId: params.id\n                }, {\n                    headers: {\n                        Authorization: `Bearer ${token}`,\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                if (response.data.success) {\n                    setIsFavorite(true);\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Added to favorites\");\n                }\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n            if (error.response?.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Please log in again to continue\");\n                localStorage.removeItem(\"authToken\");\n                setIsLoggedIn(false);\n                setShowLoginModal(true);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to update favorites\");\n            }\n        } finally{\n            setFavoriteLoading(false);\n        }\n    };\n    const checkLikeStatus = async (token)=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get(`/api/likes?blogId=${params.id}`, {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            if (response.data.success) {\n                setIsLiked(response.data.isLiked);\n            }\n        } catch (error) {\n            console.error(\"Error checking like status:\", error);\n        }\n    };\n    const fetchLikesCount = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get(`/api/blog/likes?id=${params.id}`);\n            if (response.data.success) {\n                setLikesCount(response.data.count);\n            }\n        } catch (error) {\n            console.error(\"Error fetching likes count:\", error);\n        }\n    };\n    const fetchTrendingBlogs = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].get(\"/api/blog/trending\");\n            if (response.data.success) {\n                setTrendingBlogs(response.data.blogs);\n            }\n        } catch (error) {\n            console.error(\"Error fetching trending blogs:\", error);\n        }\n    };\n    const toggleLike = async ()=>{\n        if (!isLoggedIn) {\n            setShowLoginModal(true);\n            return;\n        }\n        try {\n            setLikeLoading(true);\n            const token = localStorage.getItem(\"authToken\");\n            if (isLiked) {\n                // Remove like\n                const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].delete(`/api/likes?blogId=${params.id}`, {\n                    headers: {\n                        Authorization: `Bearer ${token}`\n                    }\n                });\n                if (response.data.success) {\n                    setIsLiked(false);\n                    setLikesCount((prev)=>Math.max(0, prev - 1));\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Like removed\");\n                }\n            } else {\n                // Add like\n                const response = await axios__WEBPACK_IMPORTED_MODULE_11__[\"default\"].post(\"/api/likes\", {\n                    blogId: params.id\n                }, {\n                    headers: {\n                        Authorization: `Bearer ${token}`,\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                if (response.data.success) {\n                    setIsLiked(true);\n                    setLikesCount((prev)=>prev + 1);\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Post liked\");\n                }\n            }\n        } catch (error) {\n            console.error(\"Error toggling like:\", error);\n            if (error.response?.status === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Please log in again to continue\");\n                localStorage.removeItem(\"authToken\");\n                setIsLoggedIn(false);\n                setShowLoginModal(true);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to update like\");\n            }\n        } finally{\n            setLikeLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const authToken = localStorage.getItem(\"authToken\");\n        if (authToken) {\n            setIsLoggedIn(true);\n            checkFavoriteStatus(authToken);\n            checkLikeStatus(authToken);\n        }\n        fetchLikesCount();\n        fetchTrendingBlogs();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        // Track blog post view when data is loaded\n        if (data && data._id) {\n            (0,_utils_analyticsUtils__WEBPACK_IMPORTED_MODULE_9__.trackPageView)(`/blogs/${params.id}`, \"blog\", data._id);\n        }\n    }, [\n        data,\n        params.id\n    ]);\n    // Add this function to parse blog mentions and images in the description\n    const parseBlogContent = (content)=>{\n        if (!content) return \"\";\n        let parsedContent = content;\n        // Regular expression to find blog mentions in format [[blogId|blogTitle]]\n        const mentionRegex = /\\[\\[([a-f\\d]{24})\\|([^\\]]+)\\]\\]/g;\n        // Replace mentions with clickable links\n        parsedContent = parsedContent.replace(mentionRegex, (match, blogId, blogTitle)=>{\n            return `<a href=\"/blogs/${blogId}\" class=\"text-blue-600 hover:underline\">${blogTitle}</a>`;\n        });\n        // Regular expression to find image references in format {{image:url|filename}}\n        const imageRegex = /\\{\\{image:([^|]+)\\|([^}]+)\\}\\}/g;\n        // Replace image references with actual images\n        parsedContent = parsedContent.replace(imageRegex, (match, imageUrl, filename)=>{\n            return `<div class=\"my-6 text-center\">\r\n        <img src=\"${imageUrl}\" alt=\"${filename}\" class=\"max-w-full h-auto mx-auto rounded-lg shadow-md\" style=\"max-height: 400px;\" />\r\n      </div>`;\n        });\n        return parsedContent;\n    };\n    return data ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_6__.ToastContainer, {\n                position: \"top-center\",\n                autoClose: 3000\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                lineNumber: 419,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-200 py-5 px-5 md:px-12 lg:px-28\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.logo,\n                                    width: 180,\n                                    alt: \"\",\n                                    className: \"w-[130px] sm:w-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 422,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/profile\"),\n                                        className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                src: userProfilePicture,\n                                                width: 24,\n                                                height: 24,\n                                                alt: \"Account\",\n                                                className: \"w-6 h-6 rounded-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: userName || \"Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowLoginModal(true),\n                                    className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]\",\n                                    children: [\n                                        \"Get started \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.arrow,\n                                            alt: \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 27\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 425,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 421,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center my-24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl sm:text-5xl font-semibold max-w-[700px] mx-auto\",\n                                children: data.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 455,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleFavorite,\n                                    disabled: favoriteLoading,\n                                    className: `flex items-center gap-2 px-4 py-2 rounded-full ${isFavorite ? \"bg-yellow-100 text-yellow-700 border border-yellow-300\" : \"bg-gray-100 text-gray-700 border border-gray-300\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: isFavorite ? \"currentColor\" : \"none\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                points: \"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 13\n                                        }, this),\n                                        isFavorite ? \"Favorited\" : \"Add to Favorites\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 456,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                className: \"mx-auto mt-6 rounded-full object-cover w-16 h-16 border-2 border-white\",\n                                src: data.authorImg,\n                                width: 60,\n                                height: 60,\n                                alt: \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 482,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 pb-2 text-lg max-w-[740px] mx-auto\",\n                                children: data.author\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 483,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 454,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                lineNumber: 420,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-5 max-w-[800px] md:mx-auto mt-[-100px] mb-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        className: \"border-4 border-white\",\n                        src: data.image,\n                        width: 800,\n                        height: 480,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 487,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"blog-content\",\n                        dangerouslySetInnerHTML: {\n                            __html: parseBlogContent(data.description)\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 489,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleLike,\n                            disabled: likeLoading,\n                            className: `flex items-center gap-2 px-4 py-2 rounded-full transition-all ${isLiked ? \"bg-red-100 text-red-600 border border-red-300\" : \"bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    width: \"20\",\n                                    height: \"20\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: isLiked ? \"currentColor\" : \"none\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 11\n                                }, this),\n                                isLiked ? \"Liked\" : \"Like\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                            lineNumber: 498,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 497,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-black font font-semibold my-4\",\n                                children: \"Share this article on social media\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 524,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            window.location.href = \"/\";\n                                        },\n                                        className: \"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full mr-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl\",\n                                        title: \"Share on Facebook\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M22.675 0h-21.35C.595 0 0 .592 0 1.326v21.348C0 23.406.595 24 1.325 24h11.495v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.797.143v3.24l-1.918.001c-1.504 0-1.797.715-1.797 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116C23.406 24 24 23.406 24 22.674V1.326C24 .592 23.406 0 22.675 0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            window.location.href = \"/\";\n                                        },\n                                        className: \"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full mr-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl\",\n                                        title: \"Share on Twitter\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M24 4.557a9.83 9.83 0 0 1-2.828.775 4.932 4.932 0 0 0 2.165-2.724c-.951.564-2.005.974-3.127 1.195a4.916 4.916 0 0 0-8.38 4.482C7.691 8.095 4.066 6.13 1.64 3.161c-.542.929-.856 2.01-.857 3.17 0 2.188 1.115 4.116 2.823 5.247a4.904 4.904 0 0 1-2.229-.616c-.054 2.281 1.581 4.415 3.949 4.89a4.936 4.936 0 0 1-2.224.084c.627 1.956 2.444 3.377 4.6 3.417A9.867 9.867 0 0 1 0 21.543a13.94 13.94 0 0 0 7.548 2.209c9.057 0 14.009-7.496 14.009-13.986 0-.21 0-.423-.016-.634A9.936 9.936 0 0 0 24 4.557z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            window.location.href = \"/\";\n                                        },\n                                        className: \"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full shadow-lg transition-all border border-gray-200 hover:shadow-2xl\",\n                                        title: \"Share on Google Plus\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 48 48\",\n                                            fill: \"black\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"24\",\n                                                    cy: \"24\",\n                                                    r: \"24\",\n                                                    fill: \"black\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                    x: \"13\",\n                                                    y: \"32\",\n                                                    \"font-size\": \"18\",\n                                                    \"font-family\": \"Arial, Helvetica, sans-serif\",\n                                                    fill: \"white\",\n                                                    \"font-weight\": \"bold\",\n                                                    children: \"G+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            try {\n                                                // Create a temporary input element\n                                                const tempInput = document.createElement(\"input\");\n                                                // Set its value to the current URL\n                                                tempInput.value = window.location.href;\n                                                // Append it to the document\n                                                document.body.appendChild(tempInput);\n                                                // Select its content\n                                                tempInput.select();\n                                                // Execute the copy command\n                                                document.execCommand(\"copy\");\n                                                // Remove the temporary element\n                                                document.body.removeChild(tempInput);\n                                                // Show success message with a more visible toast\n                                                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Link copied to clipboard!\", {\n                                                    position: \"top-center\",\n                                                    autoClose: 3000,\n                                                    hideProgressBar: false,\n                                                    closeOnClick: true,\n                                                    pauseOnHover: true,\n                                                    draggable: true,\n                                                    progress: undefined\n                                                });\n                                                console.log(\"Link copied to clipboard!\");\n                                            } catch (err) {\n                                                console.error(\"Copy failed:\", err);\n                                                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to copy link\");\n                                            }\n                                        },\n                                        className: \"flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full ml-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl\",\n                                        title: \"Copy link to clipboard\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                    x: \"9\",\n                                                    y: \"9\",\n                                                    width: \"13\",\n                                                    height: \"13\",\n                                                    rx: \"2\",\n                                                    ry: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                lineNumber: 525,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                        lineNumber: 523,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                lineNumber: 486,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_TrendingBlogs__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                blogs: trendingBlogs,\n                currentBlogId: params.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                lineNumber: 606,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                lineNumber: 607,\n                columnNumber: 5\n            }, this),\n            showLogoutConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-md shadow-lg max-w-sm w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Confirm Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                            lineNumber: 613,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-6\",\n                            children: \"Are you sure you want to log out? You will need to log in again to access your account.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                            lineNumber: 614,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutCancel,\n                                    className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutConfirm,\n                                    className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                            lineNumber: 615,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 612,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                lineNumber: 611,\n                columnNumber: 7\n            }, this),\n            showLoginModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-8 rounded-md shadow-lg w-96\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: isRegistering ? \"Register\" : \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                            lineNumber: 637,\n                            columnNumber: 11\n                        }, this),\n                        isRegistering ? // Registration Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleRegisterSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: registerData.email,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            name: \"password\",\n                                            value: registerData.password,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            name: \"confirmPassword\",\n                                            value: registerData.confirmPassword,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 676,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 675,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Already have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this) : // Login Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLoginSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: loginData.email,\n                                            onChange: handleLoginChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    name: \"password\",\n                                                    value: loginData.password,\n                                                    onChange: handleLoginChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md pr-10\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: togglePasswordVisibility,\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600\",\n                                                    children: showPassword ? // Eye-slash icon (hidden)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 23\n                                                    }, this) : // Eye icon (visible)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: rememberMe,\n                                                onChange: (e)=>setRememberMe(e.target.checked),\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-700\",\n                                                children: \"Remember me\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 749,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Register\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                    lineNumber: 636,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\blogs\\\\[id]\\\\page.jsx\",\n                lineNumber: 635,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true) : null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/blogs/[id]/page.jsx\n");

/***/ }),

/***/ "(ssr)/./utils/analyticsUtils.js":
/*!*********************************!*\
  !*** ./utils/analyticsUtils.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchAnalytics: () => (/* binding */ fetchAnalytics),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   trackPageView: () => (/* binding */ trackPageView)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// Track page view\nconst trackPageView = async (path, contentType = \"page\", blogId = null)=>{\n    try {\n        // For testing purposes, let's track in both dev and prod\n        // Remove this condition to only track in production\n        // if (process.env.NODE_ENV === 'development') {\n        //   console.log('Analytics tracking skipped in development mode');\n        //   return;\n        // }\n        console.log(\"Tracking page view:\", {\n            path,\n            contentType,\n            blogId\n        });\n        // Get referrer\n        const referrer = document.referrer || null;\n        // Send analytics data\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/analytics\", {\n            path,\n            contentType,\n            blogId,\n            referrer\n        });\n        console.log(\"Analytics tracking response:\", response.data);\n    } catch (error) {\n        // Silently fail - don't disrupt user experience for analytics\n        console.error(\"Analytics error:\", error);\n    }\n};\n// Format numbers for display (e.g., 1000 -> 1K)\nconst formatNumber = (num)=>{\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + \"M\";\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + \"K\";\n    }\n    return num.toString();\n};\n// Fetch analytics data (for admin dashboard)\nconst fetchAnalytics = async (period = \"7days\")=>{\n    const token = localStorage.getItem(\"authToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/api/analytics?period=${period}`, {\n        headers: {\n            Authorization: `Bearer ${token}`\n        }\n    });\n    return response.data;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/analyticsUtils.js\n");

/***/ }),

/***/ "(ssr)/./utils/cookieUtils.js":
/*!******************************!*\
  !*** ./utils/cookieUtils.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCookie: () => (/* binding */ getCookie),\n/* harmony export */   hasAcceptedCookies: () => (/* binding */ hasAcceptedCookies),\n/* harmony export */   removeCookie: () => (/* binding */ removeCookie),\n/* harmony export */   setAnalyticsCookies: () => (/* binding */ setAnalyticsCookies),\n/* harmony export */   setCookie: () => (/* binding */ setCookie),\n/* harmony export */   setFunctionalCookies: () => (/* binding */ setFunctionalCookies)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n// Set a cookie\nconst setCookie = (name, value, options = {})=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(name, value, options);\n};\n// Get a cookie\nconst getCookie = (name)=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n};\n// Remove a cookie\nconst removeCookie = (name)=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(name);\n};\n// Check if user has accepted cookies\nconst hasAcceptedCookies = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"cookie-consent\") === \"accepted\";\n};\n// Set analytics cookies (only if user has accepted)\nconst setAnalyticsCookies = ()=>{\n    if (hasAcceptedCookies()) {\n    // Set analytics cookies here\n    // Example: Cookies.set('_ga', 'GA1.2.123456789.1234567890', { expires: 365 })\n    }\n};\n// Set functional cookies (only if user has accepted)\nconst setFunctionalCookies = ()=>{\n    if (hasAcceptedCookies()) {\n    // Set functional cookies here\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/cookieUtils.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1db334ffb0c5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vYXBwL2dsb2JhbHMuY3NzP2M5ZWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZGIzMzRmZmIwYzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./Components/CookieConsent.jsx":
/*!**************************************!*\
  !*** ./Components/CookieConsent.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\Components\CookieConsent.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/blogs/[id]/page.jsx":
/*!*********************************!*\
  !*** ./app/blogs/[id]/page.jsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\blogs\[id]\page.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.jsx":
/*!************************!*\
  !*** ./app/layout.jsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.jsx\",\"import\":\"Outfit\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"outfit\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.jsx\\\",\\\"import\\\":\\\"Outfit\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"outfit\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _Components_CookieConsent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/Components/CookieConsent */ \"(rsc)/./Components/CookieConsent.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(rsc)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(rsc)/./node_modules/react-toastify/dist/ReactToastify.css\");\n\n\n\n\n\n\n// In production, use the pre-built CSS file\nconst isProd = \"development\" === \"production\";\nconst cssPath = isProd ? \"/build/tailwind.css\" : \"./globals.css\";\nconst metadata = {\n    title: \"Mr.Blogger\",\n    description: \"A blog platform by Mr.Blogger\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: isProd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: cssPath\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                    lineNumber: 22,\n                    columnNumber: 20\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_5___default().className),\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_CookieConsent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                        position: \"top-center\",\n                        autoClose: 3000\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.jsx\n");

/***/ }),

/***/ "(ssr)/./Assets/add_icon.png":
/*!*****************************!*\
  !*** ./Assets/add_icon.png ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/add_icon.17426346.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fadd_icon.17426346.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYWRkX2ljb24ucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLGtNQUFrTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYWRkX2ljb24ucG5nP2EzMDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2FkZF9pY29uLjE3NDI2MzQ2LnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFkZF9pY29uLjE3NDI2MzQ2LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/add_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/arrow.png":
/*!**************************!*\
  !*** ./Assets/arrow.png ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/arrow.35bdbbc1.png\",\"height\":16,\"width\":18,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Farrow.35bdbbc1.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":7});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYXJyb3cucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYXJyb3cucG5nPzYxZjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Fycm93LjM1YmRiYmMxLnBuZ1wiLFwiaGVpZ2h0XCI6MTYsXCJ3aWR0aFwiOjE4LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFycm93LjM1YmRiYmMxLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo3fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/arrow.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_icon.png":
/*!******************************!*\
  !*** ./Assets/blog_icon.png ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_icon.6cf97bbc.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_icon.6cf97bbc.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxvTUFBb00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL2Jsb2dfaWNvbi5wbmc/MjJlYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvYmxvZ19pY29uLjZjZjk3YmJjLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfaWNvbi42Y2Y5N2JiYy5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_1.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_1.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_1.4406e300.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_1.4406e300.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18xLnBuZz9lMTk3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18xLjQ0MDZlMzAwLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEuNDQwNmUzMDAucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_1.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_10.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_10.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_10.b87908bf.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_10.b87908bf.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTAucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTAucG5nPzA0NzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEwLmI4NzkwOGJmLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEwLmI4NzkwOGJmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_10.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_11.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_11.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_11.ffa40298.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_11.ffa40298.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTEucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTEucG5nPzAxMDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzExLmZmYTQwMjk4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzExLmZmYTQwMjk4LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_11.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_12.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_12.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_12.e5886225.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_12.e5886225.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTIucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTIucG5nPzQ3ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEyLmU1ODg2MjI1LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEyLmU1ODg2MjI1LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_12.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_13.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_13.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_13.d4a89f0e.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_13.d4a89f0e.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTMucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTMucG5nPzJjNDgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEzLmQ0YTg5ZjBlLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEzLmQ0YTg5ZjBlLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_13.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_14.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_14.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_14.0d239c78.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_14.0d239c78.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTQucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTQucG5nPzMxNzciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE0LjBkMjM5Yzc4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE0LjBkMjM5Yzc4LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_14.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_15.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_15.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_15.1d85ee32.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_15.1d85ee32.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTUucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTUucG5nPzYyOGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE1LjFkODVlZTMyLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE1LjFkODVlZTMyLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_15.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_16.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_16.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_16.ffb19842.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_16.ffb19842.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTYucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTYucG5nPzU5NGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE2LmZmYjE5ODQyLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE2LmZmYjE5ODQyLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_16.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_2.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_2.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_2.b986ae66.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_2.b986ae66.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18yLnBuZz9mZWI1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18yLmI5ODZhZTY2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzIuYjk4NmFlNjYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_2.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_3.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_3.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_3.5e7fff80.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_3.5e7fff80.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMy5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18zLnBuZz9kZWRlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18zLjVlN2ZmZjgwLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzMuNWU3ZmZmODAucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_3.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_4.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_4.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_4.86f96556.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_4.86f96556.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY180LnBuZz8yMGEyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY180Ljg2Zjk2NTU2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzQuODZmOTY1NTYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_4.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_5.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_5.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_5.144896ce.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_5.144896ce.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY181LnBuZz82YjE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY181LjE0NDg5NmNlLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzUuMTQ0ODk2Y2UucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_5.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_6.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_6.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_6.b530ea03.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_6.b530ea03.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY182LnBuZz84ZWQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY182LmI1MzBlYTAzLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzYuYjUzMGVhMDMucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_6.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_7.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_7.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_7.3dcf8c5f.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_7.3dcf8c5f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNy5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY183LnBuZz9mNGJjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY183LjNkY2Y4YzVmLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzcuM2RjZjhjNWYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_7.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_8.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_8.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_8.50101226.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_8.50101226.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfOC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY184LnBuZz85MGQ2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY184LjUwMTAxMjI2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzguNTAxMDEyMjYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_8.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_9.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_9.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_9.56aa9ce8.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_9.56aa9ce8.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfOS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY185LnBuZz9lODQyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY185LjU2YWE5Y2U4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzkuNTZhYTljZTgucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_9.png\n");

/***/ }),

/***/ "(ssr)/./Assets/email_icon.png":
/*!*******************************!*\
  !*** ./Assets/email_icon.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/email_icon.4caec7c6.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Femail_icon.4caec7c6.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZW1haWxfaWNvbi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsc01BQXNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9lbWFpbF9pY29uLnBuZz82MmY1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9lbWFpbF9pY29uLjRjYWVjN2M2LnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmVtYWlsX2ljb24uNGNhZWM3YzYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/email_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/facebook_icon.png":
/*!**********************************!*\
  !*** ./Assets/facebook_icon.png ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/facebook_icon.cbcfc36d.png\",\"height\":58,\"width\":58,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffacebook_icon.cbcfc36d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZmFjZWJvb2tfaWNvbi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsNE1BQTRNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9mYWNlYm9va19pY29uLnBuZz85ZDVmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mYWNlYm9va19pY29uLmNiY2ZjMzZkLnBuZ1wiLFwiaGVpZ2h0XCI6NTgsXCJ3aWR0aFwiOjU4LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmZhY2Vib29rX2ljb24uY2JjZmMzNmQucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/facebook_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/googleplus_icon.png":
/*!************************************!*\
  !*** ./Assets/googleplus_icon.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/googleplus_icon.15e2de32.png\",\"height\":59,\"width\":59,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fgoogleplus_icon.15e2de32.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZ29vZ2xlcGx1c19pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxnTkFBZ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL2dvb2dsZXBsdXNfaWNvbi5wbmc/OWM1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZ29vZ2xlcGx1c19pY29uLjE1ZTJkZTMyLnBuZ1wiLFwiaGVpZ2h0XCI6NTksXCJ3aWR0aFwiOjU5LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmdvb2dsZXBsdXNfaWNvbi4xNWUyZGUzMi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/googleplus_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/logo.png":
/*!*************************!*\
  !*** ./Assets/logo.png ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo.c649e147.png\",\"height\":53,\"width\":186,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.c649e147.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":2});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvbG9nby5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsMkxBQTJMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9sb2dvLnBuZz9jOWE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9sb2dvLmM2NDllMTQ3LnBuZ1wiLFwiaGVpZ2h0XCI6NTMsXCJ3aWR0aFwiOjE4NixcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvLmM2NDllMTQ3LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjoyfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/logo.png\n");

/***/ }),

/***/ "(ssr)/./Assets/logo_light.png":
/*!*******************************!*\
  !*** ./Assets/logo_light.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo_light.9ce1f99e.png\",\"height\":55,\"width\":201,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo_light.9ce1f99e.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":2});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvbG9nb19saWdodC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsdU1BQXVNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9sb2dvX2xpZ2h0LnBuZz83NWI0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9sb2dvX2xpZ2h0LjljZTFmOTllLnBuZ1wiLFwiaGVpZ2h0XCI6NTUsXCJ3aWR0aFwiOjIwMSxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvX2xpZ2h0LjljZTFmOTllLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjoyfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/logo_light.png\n");

/***/ }),

/***/ "(ssr)/./Assets/profile_icon.png":
/*!*********************************!*\
  !*** ./Assets/profile_icon.png ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/profile_icon.fa2679c4.png\",\"height\":92,\"width\":92,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprofile_icon.fa2679c4.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvcHJvZmlsZV9pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywwTUFBME0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL3Byb2ZpbGVfaWNvbi5wbmc/NmI3NyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvcHJvZmlsZV9pY29uLmZhMjY3OWM0LnBuZ1wiLFwiaGVpZ2h0XCI6OTIsXCJ3aWR0aFwiOjkyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnByb2ZpbGVfaWNvbi5mYTI2NzljNC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/profile_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/twitter_icon.png":
/*!*********************************!*\
  !*** ./Assets/twitter_icon.png ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/twitter_icon.0d1dc581.png\",\"height\":59,\"width\":59,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ftwitter_icon.0d1dc581.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvdHdpdHRlcl9pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywwTUFBME0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL3R3aXR0ZXJfaWNvbi5wbmc/NDI5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvdHdpdHRlcl9pY29uLjBkMWRjNTgxLnBuZ1wiLFwiaGVpZ2h0XCI6NTksXCJ3aWR0aFwiOjU5LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnR3aXR0ZXJfaWNvbi4wZDFkYzU4MS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/twitter_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/upload_area.png":
/*!********************************!*\
  !*** ./Assets/upload_area.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/upload_area.1ee5fe3d.png\",\"height\":140,\"width\":240,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fupload_area.1ee5fe3d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvdXBsb2FkX2FyZWEucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDBNQUEwTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvdXBsb2FkX2FyZWEucG5nP2RhZTMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL3VwbG9hZF9hcmVhLjFlZTVmZTNkLnBuZ1wiLFwiaGVpZ2h0XCI6MTQwLFwid2lkdGhcIjoyNDAsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGdXBsb2FkX2FyZWEuMWVlNWZlM2QucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/upload_area.png\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9hcHAvZmF2aWNvbi5pY28/YWVmZiJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/ms","vendor-chunks/react-toastify","vendor-chunks/js-cookie","vendor-chunks/clsx","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/supports-color","vendor-chunks/delayed-stream","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fblogs%2F%5Bid%5D%2Fpage&page=%2Fblogs%2F%5Bid%5D%2Fpage&appPaths=%2Fblogs%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fblogs%2F%5Bid%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();