"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./Components/Header.jsx":
/*!*******************************!*\
  !*** ./Components/Header.jsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Assets/assets */ \"(app-pages-browser)/./Assets/assets.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Header = (param)=>{\n    let { setSearchTerm } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLoginModal, setShowLoginModal] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isRegistering, setIsRegistering] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showAccountDropdown, setShowAccountDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [loginData, setLoginData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [registerData, setRegisterData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        role: \"user\"\n    });\n    const [userProfilePicture, setUserProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"/default_profile.png\");\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showLogoutConfirm, setShowLogoutConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showRegisterPassword, setShowRegisterPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [showRegisterConfirmPassword, setShowRegisterConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isSearchMode, setIsSearchMode] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [localSearchTerm, setLocalSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // Add this function to toggle password visibility\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    // Check if user is logged in on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const authToken = localStorage.getItem(\"authToken\");\n        const storedUserRole = localStorage.getItem(\"userRole\");\n        const storedUserId = localStorage.getItem(\"userId\");\n        const storedProfilePicture = localStorage.getItem(\"userProfilePicture\");\n        const storedUserName = localStorage.getItem(\"userName\");\n        if (authToken) {\n            setIsLoggedIn(true);\n            setUserRole(storedUserRole || \"user\");\n            if (storedProfilePicture) {\n                setUserProfilePicture(storedProfilePicture);\n            }\n            if (storedUserName) {\n                setUserName(storedUserName);\n            }\n        }\n    }, []);\n    const onSubmitHandler = async (e)=>{\n        // Existing email subscription code\n        e.preventDefault();\n        const formData = new FormData();\n        formData.append(\"email\", email);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/email\", formData);\n        if (response.data.success) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(response.data.msg);\n            setEmail(\"\");\n        } else {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Error\");\n        }\n    };\n    const handleLoginChange = (e)=>{\n        setLoginData({\n            ...loginData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleRegisterChange = (e)=>{\n        setRegisterData({\n            ...registerData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleLoginSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/auth\", {\n                email: loginData.email,\n                password: loginData.password\n            });\n            if (response.data.success) {\n                // Store auth data in localStorage\n                localStorage.setItem(\"authToken\", response.data.token || \"dummy-token\");\n                localStorage.setItem(\"userRole\", response.data.user.role);\n                localStorage.setItem(\"userId\", response.data.user.id);\n                localStorage.setItem(\"userProfilePicture\", response.data.user.profilePicture);\n                localStorage.setItem(\"userName\", response.data.user.name || \"\");\n                // Update state\n                setIsLoggedIn(true);\n                setUserRole(response.data.user.role);\n                setUserProfilePicture(response.data.user.profilePicture);\n                setUserName(response.data.user.name || \"\");\n                setShowLoginModal(false);\n                // Check if user is admin\n                if (response.data.user.role === \"admin\") {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    window.location.href = \"/admin\";\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful\");\n                    // Redirect regular users to homepage or user dashboard\n                    window.location.href = \"/\";\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Invalid credentials\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Login error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Login failed\");\n        }\n    };\n    const handleRegisterSubmit = async (e)=>{\n        e.preventDefault();\n        // Validate passwords match\n        if (registerData.password !== registerData.confirmPassword) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Passwords do not match\");\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"/api/register\", {\n                email: registerData.email,\n                password: registerData.password,\n                role: registerData.role // Always \"user\" for public registration\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Registration successful! Please login.\");\n                // Switch back to login form\n                setIsRegistering(false);\n                // Pre-fill email in login form\n                setLoginData({\n                    ...loginData,\n                    email: registerData.email\n                });\n                // Reset register form\n                setRegisterData({\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\",\n                    role: \"user\"\n                });\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(response.data.message || \"Registration failed\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Registration error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Registration failed\");\n        }\n    };\n    const toggleForm = ()=>{\n        setIsRegistering(!isRegistering);\n    };\n    const handleLogoutClick = ()=>{\n        setShowLogoutConfirm(true);\n        setShowAccountDropdown(false);\n    };\n    const handleLogoutConfirm = ()=>{\n        // Clear auth data from localStorage\n        localStorage.removeItem(\"authToken\");\n        localStorage.removeItem(\"userRole\");\n        localStorage.removeItem(\"userId\");\n        localStorage.removeItem(\"userProfilePicture\");\n        localStorage.removeItem(\"userName\");\n        // Update state\n        setIsLoggedIn(false);\n        setUserRole(\"\");\n        setShowLogoutConfirm(false);\n        // Show toast and wait briefly before redirecting\n        react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Logged out successfully\");\n        // Add a small delay before any navigation\n        setTimeout(()=>{\n            window.location.href = \"/\";\n        }, 300);\n    };\n    const handleLogoutCancel = ()=>{\n        setShowLogoutConfirm(false);\n    };\n    const toggleAccountDropdown = ()=>{\n        setShowAccountDropdown(!showAccountDropdown);\n    };\n    // Search handler (for now, just alert or log, you can wire to blog list)\n    const onSearchHandler = (e)=>{\n        e.preventDefault();\n        if (!localSearchTerm.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Please enter a search term\");\n            return;\n        }\n        setSearchTerm(localSearchTerm);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-5 px-5 md:px-12 lg:px-28\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.logo,\n                        width: 180,\n                        alt: \"\",\n                        className: \"w-[130px] sm:w-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/profile\"),\n                                className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: userProfilePicture,\n                                        width: 24,\n                                        height: 24,\n                                        alt: \"Account\",\n                                        className: \"w-6 h-6 rounded-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: userName || \"Account\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowLoginModal(true),\n                            className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-solid border-black shadow-[-7px_7px_0px_#000000]\",\n                            children: [\n                                \"Get started \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: _Assets_assets__WEBPACK_IMPORTED_MODULE_1__.assets.arrow,\n                                    alt: \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 27\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, undefined),\n            showLoginModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-8 rounded-md shadow-lg w-96\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: isRegistering ? \"Register\" : \"Login\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, undefined),\n                        isRegistering ? // Registration Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleRegisterSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: registerData.email,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            name: \"password\",\n                                            value: registerData.password,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            name: \"confirmPassword\",\n                                            value: registerData.confirmPassword,\n                                            onChange: handleRegisterChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Already have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 257,\n                            columnNumber: 15\n                        }, undefined) : // Login Form\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLoginSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: loginData.email,\n                                            onChange: handleLoginChange,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    name: \"password\",\n                                                    value: loginData.password,\n                                                    onChange: handleLoginChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md pr-10\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: togglePasswordVisibility,\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600\",\n                                                    children: showPassword ? // Eye-slash icon (hidden)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 25\n                                                    }, undefined) : // Eye icon (visible)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-5 w-5\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"bg-black text-white px-4 py-2 rounded-md\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowLoginModal(false),\n                                            className: \"text-gray-600 px-4 py-2\",\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleForm,\n                                                className: \"text-blue-600 hover:underline\",\n                                                children: \"Register\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 321,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                    lineNumber: 252,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, undefined),\n            showLogoutConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-md shadow-lg max-w-sm w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Confirm Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-6\",\n                            children: \"Are you sure you want to log out? You will need to log in again to access your account.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 403,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutCancel,\n                                    className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutConfirm,\n                                    className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                    lineNumber: 401,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center my-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl sm:text-5xl font-medium\",\n                        children: \"Mr.Blogger\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-10 max-w-[740px] m-auto text-xs sm:text-base\",\n                        children: \"Discover insightful articles, trending tech news, startup journeys, and lifestyle stories — all in one place. Welcome to your daily dose of inspiration and knowledge.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 424,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: isSearchMode ? onSearchHandler : onSubmitHandler,\n                        className: \"flex justify-between max-w-[500px] scale-75 sm:scale-100 mx-auto mt-10 border border-black shadow-[-7px_7px_0px_#000000]\",\n                        action: \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                onChange: isSearchMode ? (e)=>{\n                                    setLocalSearchTerm(e.target.value);\n                                    if (e.target.value === \"\") setSearchTerm(\"\");\n                                } : (e)=>setEmail(e.target.value),\n                                value: isSearchMode ? localSearchTerm : email,\n                                type: isSearchMode ? \"text\" : \"email\",\n                                placeholder: isSearchMode ? \"Search blogs...\" : \"Enter your email\",\n                                className: \"pl-4 outline-none flex-1\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"border-l border-black py-4 px-4 sm:px-8 active:bg-gray-600 active:text-white\",\n                                children: isSearchMode ? \"Search\" : \"Subscribe\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setIsSearchMode((prev)=>{\n                                                if (prev) {\n                                                    setLocalSearchTerm(\"\");\n                                                    setSearchTerm(\"\"); // Clear parent search when toggling back\n                                                }\n                                                return !prev;\n                                            });\n                                        },\n                                        onMouseEnter: ()=>setShowTooltip(true),\n                                        onMouseLeave: ()=>setShowTooltip(false),\n                                        className: \"flex items-center justify-center px-4 border-l border-black hover:bg-gray-100 transition-colors\",\n                                        style: {\n                                            minWidth: \"56px\"\n                                        },\n                                        children: isSearchMode ? // Mail/Envelope Icon\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"22\",\n                                            height: \"22\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                    x: \"3\",\n                                                    y: \"5\",\n                                                    width: \"18\",\n                                                    height: \"14\",\n                                                    rx: \"2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                    points: \"3,7 12,13 21,7\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, undefined) : // Search Icon\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            width: \"22\",\n                                            height: \"22\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: \"11\",\n                                                    cy: \"11\",\n                                                    r: \"8\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    fill: \"none\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"21\",\n                                                    y1: \"21\",\n                                                    x2: \"16.65\",\n                                                    y2: \"16.65\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 -translate-x-1/2 bg-black text-white text-xs rounded px-3 py-1 shadow-lg animate-fade-in z-10 whitespace-nowrap\",\n                                        children: isSearchMode ? \"Switch to Subscribe mode\" : \"Switch to Search mode\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\Header.jsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"iexpWvhRv7F7lDRJbJ4iOk3P4RQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Header;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/Header.jsx\n"));

/***/ })

});