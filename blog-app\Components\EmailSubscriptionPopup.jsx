'use client'
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

const EmailSubscriptionPopup = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Check if user has already subscribed (permanent dismissal)
    const hasSubscribed = localStorage.getItem('emailSubscribed');
    if (hasSubscribed === 'true') {
      return;
    }

    // Check if user has closed the popup recently
    const lastClosedTime = localStorage.getItem('emailPopupLastClosed');
    const now = Date.now();

    // If popup was closed less than 10 minutes ago, don't show it
    // For testing: use 10000ms (10 seconds), for production: use 600000ms (10 minutes)
    if (lastClosedTime && (now - parseInt(lastClosedTime)) < 10000) { // 10 seconds for testing - change to 600000 for production
      return;
    }

    // Show popup after 2 minutes (120000 milliseconds)
    // For testing: use 5000ms (5 seconds), for production: use 120000ms (2 minutes)
    const timer = setTimeout(() => {
      setShowPopup(true);
    }, 5000); // 5 seconds for testing - change to 120000 for production

    // Cleanup timer on component unmount
    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setShowPopup(false);
    // Remember when user closed the popup (timestamp)
    localStorage.setItem('emailPopupLastClosed', Date.now().toString());
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('email', email);
      
      const response = await axios.post('/api/email', formData);
      
      if (response.data.success) {
        toast.success('Successfully subscribed to our newsletter!');
        setShowPopup(false);
        setEmail('');
        // Remember that user has subscribed
        localStorage.setItem('emailSubscribed', 'true');
      } else {
        toast.error('Subscription failed. Please try again.');
      }
    } catch (error) {
      console.error('Subscription error:', error);
      toast.error('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!showPopup) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl max-w-md w-full relative overflow-hidden">
        {/* Close button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
          aria-label="Close popup"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Content */}
        <div className="p-8">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              SUBSCRIBE NOW
            </h2>
            <p className="text-gray-600 text-sm">
              DON'T MISS OUT ON THE LATEST BLOG POSTS<br />
              AND OFFERS.
            </p>
            <p className="text-xs text-gray-500 mt-2">
              Be the first to get notified.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email address"
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                required
              />
            </div>
            
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-black text-white py-3 px-6 rounded-md hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'SUBSCRIBING...' : 'SUBSCRIBE'}
            </button>
          </form>

          <p className="text-xs text-gray-500 text-center mt-4">
            You can unsubscribe at any time. We respect your privacy.
          </p>
        </div>
      </div>
    </div>
  );
};

export default EmailSubscriptionPopup;
